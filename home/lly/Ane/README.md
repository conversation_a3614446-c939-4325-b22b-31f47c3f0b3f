# Ane - Web Analyzer

🔍 一个功能强大的网页分析工具，支持批量域名处理、前端框架检测和工作流编排。

## 快速开始

1. 安装依赖: pip install -r requirements.txt
2. 启动应用: ./start.sh 或 streamlit run main.py
3. 访问界面: http://localhost:8501

## 功能特性

- 批量域名处理和源码获取
- 前端框架检测（React、Vue、Angular等）
- Source Map 文件检测
- 工作流编排和步骤管理
- 数据持久化存储
- 可视化Web界面

## 项目结构

- main.py: 主程序入口
- src/: 源码目录
- ui/: UI组件
- config/: 配置文件
- database/: 数据库文件
- requirements.txt: 依赖列表

