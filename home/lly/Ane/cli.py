#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ane Web Analyzer 命令行版本
用于在没有Web界面时进行基本操作
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.workflow_manager import WorkflowManager

def print_banner():
    print()
    print("🔍 Ane Web Analyzer - 命令行版本")
    print("=" * 40)
    print()

def print_menu():
    print("📋 可用操作:")
    print("  1. 查看工作流状态")
    print("  2. 执行域名获取")
    print("  3. 执行框架分析")
    print("  4. 查看结果统计")
    print("  5. 导出结果")
    print("  6. 清空数据")
    print("  0. 退出")
    print()

def show_workflow_status(wm):
    print("📊 工作流状态:")
    status = wm.get_workflow_status()
    print(f"  状态: {status['status']}")
    print(f"  总步骤: {status['total_steps']}")
    print(f"  已完成: {status['completed_steps']}")
    
    steps = wm.get_available_steps()
    print()
    print("📝 可用步骤:")
    for step_id, info in steps.items():
        print(f"  - {info['name']}: {info['description']}")
        print(f"    状态: {info['status']}, 结果数: {info['results_count']}")

def run_domain_fetch(wm):
    print("🌐 域名源码获取")
    print("请输入域名列表 (每行一个，空行结束):")
    
    domains = []
    while True:
        domain = input().strip()
        if not domain:
            break
        domains.append(domain)
    
    if not domains:
        print("❌ 未输入任何域名")
        return
    
    print(f"\n🚀 开始获取 {len(domains)} 个域名的源码...")
    
    try:
        result = wm.run_step('domain_fetcher', {'domains': domains})
        
        if result['success']:
            print(f"✅ {result['message']}")
            print(f"   成功: {result['success_count']}/{result['total']}")
            
            # 保存到数据库
            wm.db_manager.save_domain_results(result['data'])
            print("💾 结果已保存到数据库")
        else:
            print(f"❌ {result['message']}")
    
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")

def run_framework_analysis(wm):
    print("🔧 前端框架分析")
    
    # 检查是否有域名获取结果
    domain_results = wm.get_step_results('domain_fetcher')
    
    if not domain_results:
        print("❌ 请先执行域名源码获取")
        return
    
    successful_results = [r for r in domain_results if r.get('success', False)]
    print(f"📊 找到 {len(successful_results)} 个成功获取源码的域名")
    
    if not successful_results:
        print("❌ 没有成功获取的源码可供分析")
        return
    
    print("🔍 开始分析前端框架...")
    
    try:
        result = wm.run_step('framework_analyzer', {'source_data': successful_results})
        
        if result['success']:
            print(f"✅ {result['message']}")
            print(f"   已分析: {result['analyzed_count']} 个域名")
            
            # 保存到数据库
            wm.db_manager.save_framework_analysis(result['data'])
            print("💾 结果已保存到数据库")
        else:
            print(f"❌ {result['message']}")
    
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

def show_statistics(wm):
    print("📈 结果统计")
    
    try:
        stats = wm.db_manager.get_statistics()
        
        print(f"  总域名数: {stats['total_domains']}")
        print(f"  成功获取: {stats['successful_domains']}")
        print(f"  已分析: {stats['analyzed_domains']}")
        print(f"  含Source Map: {stats['sourcemap_count']}")
        print(f"  成功率: {stats['success_rate']:.1f}%")
        
        if stats['analyzed_domains'] > 0:
            print(f"  Source Map率: {stats['sourcemap_rate']:.1f}%")
    
    except Exception as e:
        print(f"❌ 获取统计失败: {str(e)}")

def export_results(wm):
    print("📁 导出结果")
    
    try:
        export_path = wm.db_manager.export_to_json()
        print(f"✅ 数据已导出到: {export_path}")
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")

def clear_data(wm):
    print("🗑️ 清空数据")
    
    confirm = input("确认清空所有数据? (yes/no): ").strip().lower()
    if confirm in ['yes', 'y']:
        try:
            wm.db_manager.clear_all_data()
            wm.reset_workflow()
            print("✅ 所有数据已清空")
        except Exception as e:
            print(f"❌ 清空失败: {str(e)}")
    else:
        print("❌ 操作已取消")

def main():
    print_banner()
    
    try:
        wm = WorkflowManager()
        print("✅ 工作流管理器初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        return
    
    while True:
        print()
        print_menu()
        
        try:
            choice = input("请选择操作 (0-6): ").strip()
            print()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                show_workflow_status(wm)
            elif choice == '2':
                run_domain_fetch(wm)
            elif choice == '3':
                run_framework_analysis(wm)
            elif choice == '4':
                show_statistics(wm)
            elif choice == '5':
                export_results(wm)
            elif choice == '6':
                clear_data(wm)
            else:
                print("❌ 无效选择，请输入 0-6")
        
        except KeyboardInterrupt:
            print("\n\n👋 程序被中断，再见!")
            break
        except Exception as e:
            print(f"❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()

