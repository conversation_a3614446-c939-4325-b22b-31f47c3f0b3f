# 🚀 Ane 安装指南 - 使用指定虚拟环境

## 快速安装

### 1. 激活现有虚拟环境
```bash
# 激活您的虚拟环境
source ~/pro/pyvenv/bin/activate

# 确认环境激活
which python
# 应该显示: /home/<USER>/pro/pyvenv/bin/python
```

### 2. 安装项目依赖
```bash
# 进入项目目录
cd /home/<USER>/Ane

# 安装必需依赖
pip install streamlit requests beautifulsoup4 pandas plotly

# 或使用requirements文件
pip install -r requirements-minimal.txt
```

### 3. 启动应用
```bash
# 方式1: 使用启动脚本 (推荐)
./start.sh

# 方式2: 直接运行 (需要先激活虚拟环境)
source ~/pro/pyvenv/bin/activate
streamlit run main.py
```

### 4. 访问界面
```
http://localhost:8501
```

## 虚拟环境管理

### 检查虚拟环境状态
```bash
# 检查是否存在
ls -la ~/pro/pyvenv/

# 检查Python版本
~/pro/pyvenv/bin/python --version

# 检查已安装的包
~/pro/pyvenv/bin/pip list
```

### 如果虚拟环境有问题
```bash
# 重新创建虚拟环境
rm -rf ~/pro/pyvenv
python3 -m venv ~/pro/pyvenv

# 激活并安装依赖
source ~/pro/pyvenv/bin/activate
pip install --upgrade pip
pip install -r requirements-minimal.txt
```

## 依赖说明

### 最小依赖包 (5个)
- **streamlit**: Web UI 框架
- **requests**: HTTP 请求库  
- **beautifulsoup4**: HTML 解析
- **pandas**: 数据处理
- **plotly**: 数据可视化

### 安装验证
```bash
# 激活环境
source ~/pro/pyvenv/bin/activate

# 测试导入
python -c "import streamlit, requests, bs4, pandas, plotly; print('✅ 所有依赖正常')"
```

## 命令行使用

如果Web界面有问题，可以使用命令行版本：
```bash
source ~/pro/pyvenv/bin/activate
python cli.py
```

## 故障排除

### 1. 权限问题
```bash
# 确保虚拟环境有执行权限
chmod +x ~/pro/pyvenv/bin/*
```

### 2. 依赖冲突
```bash
# 清理并重新安装
pip uninstall streamlit requests beautifulsoup4 pandas plotly -y
pip install streamlit requests beautifulsoup4 pandas plotly
```

### 3. 网络问题
```bash
# 使用国内镜像
pip install -r requirements-minimal.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

项目已针对您的虚拟环境环境优化配置！

