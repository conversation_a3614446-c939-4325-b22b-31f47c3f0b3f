#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器
"""

import sqlite3
import json
from typing import Dict, List, Any
from datetime import datetime
import os
from pathlib import Path

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'database', 'analyzer.db')
        
        self.db_path = db_path
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建域名表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS domains (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    domain TEXT UNIQUE NOT NULL,
                    status TEXT NOT NULL,
                    source_code TEXT,
                    response_time REAL,
                    status_code INTEGER,
                    content_length INTEGER,
                    final_url TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建框架分析表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS framework_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    domain TEXT NOT NULL,
                    frameworks TEXT,  -- JSON格式存储
                    has_sourcemap BOOLEAN,
                    sourcemap_urls TEXT,  -- JSON格式存储
                    bundler TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (domain) REFERENCES domains (domain)
                )
            """)
            
            # 创建工作流历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS workflow_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    step_id TEXT NOT NULL,
                    step_name TEXT NOT NULL,
                    params TEXT,  -- JSON格式存储
                    result_summary TEXT,  -- JSON格式存储
                    execution_time REAL,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def save_domain_results(self, results: List[Dict[str, Any]]):
        """保存域名获取结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for result in results:
                cursor.execute("""
                    INSERT OR REPLACE INTO domains 
                    (domain, status, source_code, response_time, status_code, 
                     content_length, final_url, error_message, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    result['domain'],
                    'success' if result['success'] else 'failed',
                    result.get('source_code', ''),
                    result.get('response_time', 0),
                    result.get('status_code', 0),
                    result.get('content_length', 0),
                    result.get('final_url', ''),
                    result.get('error', ''),
                    datetime.now().isoformat()
                ))
            
            conn.commit()
    
    def save_framework_analysis(self, results: List[Dict[str, Any]]):
        """保存框架分析结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for result in results:
                cursor.execute("""
                    INSERT INTO framework_analysis 
                    (domain, frameworks, has_sourcemap, sourcemap_urls, bundler)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    result['domain'],
                    json.dumps(result['frameworks'], ensure_ascii=False),
                    result['has_sourcemap'],
                    json.dumps(result['sourcemap_urls'], ensure_ascii=False),
                    result.get('bundler', 'unknown')
                ))
            
            conn.commit()
    
    def get_domain_results(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取域名结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM domains ORDER BY created_at DESC"
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in rows]
    
    def get_framework_analysis(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取框架分析结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            query = "SELECT * FROM framework_analysis ORDER BY created_at DESC"
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            results = []
            columns = [desc[0] for desc in cursor.description]
            for row in rows:
                result = dict(zip(columns, row))
                # 解析JSON字段
                result['frameworks'] = json.loads(result['frameworks'])
                result['sourcemap_urls'] = json.loads(result['sourcemap_urls'])
                results.append(result)
            
            return results
    
    def export_to_json(self, filename: str = None) -> str:
        """导出数据到JSON文件"""
        if filename is None:
            filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = {
            'domains': self.get_domain_results(),
            'framework_analysis': self.get_framework_analysis(),
            'export_time': datetime.now().isoformat()
        }
        
        export_path = os.path.join(os.path.dirname(self.db_path), filename)
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        return export_path
    
    def clear_all_data(self):
        """清空所有数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM workflow_history")
            cursor.execute("DELETE FROM framework_analysis")
            cursor.execute("DELETE FROM domains")
            conn.commit()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 域名统计
            cursor.execute("SELECT COUNT(*) FROM domains")
            total_domains = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM domains WHERE status = 'success'")
            successful_domains = cursor.fetchone()[0]
            
            # 框架分析统计
            cursor.execute("SELECT COUNT(*) FROM framework_analysis")
            analyzed_domains = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM framework_analysis WHERE has_sourcemap = 1")
            sourcemap_count = cursor.fetchone()[0]
            
            return {
                'total_domains': total_domains,
                'successful_domains': successful_domains,
                'analyzed_domains': analyzed_domains,
                'sourcemap_count': sourcemap_count,
                'success_rate': (successful_domains / total_domains * 100) if total_domains > 0 else 0,
                'sourcemap_rate': (sourcemap_count / analyzed_domains * 100) if analyzed_domains > 0 else 0
            }
