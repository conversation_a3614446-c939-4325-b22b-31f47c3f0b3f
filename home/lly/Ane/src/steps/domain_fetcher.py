#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版域名获取器 - 使用浏览器获取完整JS内容
"""

import logging
import os
import time
import json
import re
import html5lib
from typing import Dict, List, Any, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import urlparse, urljoin
from .base_step import BaseStep

# 配置日志
def setup_logger():
    logger = logging.getLogger('DomainFetcher')
    logger.setLevel(logging.INFO)
    
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 文件处理器
    file_handler = logging.FileHandler(os.path.join(log_dir, 'domain_fetcher.log'))
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 格式化
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

logger = setup_logger()

class DomainFetcher(BaseStep):
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        self.timeout = 60  # 增加超时时间
        self.headless = True  # 默认使用无头模式
        
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if 'domains' not in params:
            return False
        domains = params['domains']
        return isinstance(domains, list) and len(domains) > 0
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要domains列表")
            
            domains = params['domains']
            method = params.get('method', 'browser')
            self.headless = params.get('headless', True)
            
            cleaned_domains = self._clean_domains(domains)
            logger.info(f"开始获取 {len(cleaned_domains)} 个域名的源码，使用方法: {method}")

            if method == 'browser-use':
                results = self._fetch_with_browser_use(cleaned_domains)
            else:  # 默认使用selenium
                results = self._fetch_with_browser(cleaned_domains)
            
            for result in results:
                self.add_result(result)
            
            success_count = len([r for r in results if r['success']])
            logger.info(f"成功获取 {success_count}/{len(domains)} 个域名的源码")
            
            self._complete_execution(True, f"成功获取 {success_count}/{len(domains)} 个域名的源码")
            
            return {
                'success': True,
                'message': f"成功获取 {success_count}/{len(domains)} 个域名的源码",
                'data': results,
                'total': len(domains),
                'success_count': success_count
            }
            
        except Exception as e:
            error_msg = f"执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': [],
                'total': 0,
                'success_count': 0
            }
    
    def _clean_domains(self, domains: List[str]) -> List[str]:
        cleaned = []
        for domain in domains:
            domain = domain.strip()
            if not domain:
                continue
            if not domain.startswith(('http://', 'https://')):
                domain = 'https://' + domain
            cleaned.append(domain)
        return list(set(cleaned))
    
    def _capture_network_requests(self, driver, domain):
        """使用浏览器开发者工具捕获所有网络请求"""
        resources = {
            'scripts': [],
            'styles': [],
            'images': [],
            'other': []
        }
        
        try:
            # 获取性能日志
            logs = driver.get_log('performance')
            seen_urls = set()  # 避免重复记录
            
            for entry in logs:
                try:
                    message = json.loads(entry['message'])['message']
                    
                    # 只处理请求已完成的事件
                    if message['method'] == 'Network.responseReceived':
                        response = message['params']['response']
                        url = response['url']
                        resource_type = response.get('type', 'other')
                        
                        # 过滤掉非资源类型的请求
                        if resource_type not in ['script', 'stylesheet', 'image']:
                            continue
                        
                        # 处理所有HTTP/HTTPS资源，不限制同源
                        if url.startswith(('http://', 'https://')) and url not in seen_urls:
                            seen_urls.add(url)
                            filename = os.path.basename(urlparse(url).path)
                            
                            if resource_type == 'script':
                                resources['scripts'].append({
                                    'url': url,
                                    'filename': filename or 'script.js',
                                    'type': 'js'
                                })
                            elif resource_type == 'stylesheet':
                                resources['styles'].append({
                                    'url': url,
                                    'filename': filename or 'style.css',
                                    'type': 'css'
                                })
                            elif resource_type == 'image':
                                resources['images'].append({
                                    'url': url,
                                    'filename': filename or 'image.png',
                                    'type': 'image'
                                })
                except Exception as e:
                    logger.debug(f"解析日志条目失败: {str(e)}")
                    
            logger.info(f"捕获到 {len(resources['scripts'])} 个JS, {len(resources['styles'])} 个CSS, {len(resources['images'])} 个图片")
        
        except Exception as e:
            logger.error(f"捕获网络请求失败: {str(e)}")
        
        return resources

    def _parse_html_resources(self, html_content: str, base_url: str) -> Dict[str, List[Dict[str, str]]]:
        """解析HTML中的资源引用"""
        from urllib.parse import urljoin, urlparse
        import re

        resources = {
            'scripts': [],
            'styles': [],
            'images': [],
            'other': []
        }

        try:
            # 解析script标签
            script_pattern = r'<script[^>]*src=["\']([^"\']+)["\'][^>]*>'
            for match in re.finditer(script_pattern, html_content, re.IGNORECASE):
                src = match.group(1)
                full_url = urljoin(base_url, src)
                filename = os.path.basename(urlparse(full_url).path) or 'script.js'

                resources['scripts'].append({
                    'url': full_url,
                    'filename': filename,
                    'type': 'js'
                })

            # 解析link标签（CSS）
            link_pattern = r'<link[^>]*href=["\']([^"\']+)["\'][^>]*rel=["\']stylesheet["\'][^>]*>|<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']+)["\'][^>]*>'
            for match in re.finditer(link_pattern, html_content, re.IGNORECASE):
                href = match.group(1) or match.group(2)
                if href:
                    full_url = urljoin(base_url, href)
                    filename = os.path.basename(urlparse(full_url).path) or 'style.css'

                    resources['styles'].append({
                        'url': full_url,
                        'filename': filename,
                        'type': 'css'
                    })

            # 解析img标签
            img_pattern = r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>'
            for match in re.finditer(img_pattern, html_content, re.IGNORECASE):
                src = match.group(1)
                full_url = urljoin(base_url, src)
                filename = os.path.basename(urlparse(full_url).path) or 'image.png'

                resources['images'].append({
                    'url': full_url,
                    'filename': filename,
                    'type': 'image'
                })

            # 解析内联样式中的资源
            style_pattern = r'<style[^>]*>(.*?)</style>'
            for match in re.finditer(style_pattern, html_content, re.IGNORECASE | re.DOTALL):
                style_content = match.group(1)
                # 查找CSS中的url()引用
                url_pattern = r'url\(["\']?([^"\')\s]+)["\']?\)'
                for url_match in re.finditer(url_pattern, style_content):
                    url = url_match.group(1)
                    full_url = urljoin(base_url, url)
                    filename = os.path.basename(urlparse(full_url).path) or 'resource'

                    # 根据扩展名判断类型
                    if filename.endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp')):
                        resources['images'].append({
                            'url': full_url,
                            'filename': filename,
                            'type': 'image'
                        })
                    else:
                        resources['other'].append({
                            'url': full_url,
                            'filename': filename,
                            'type': 'resource'
                        })

            logger.info(f"从HTML解析到 {len(resources['scripts'])} 个JS, {len(resources['styles'])} 个CSS, {len(resources['images'])} 个图片")

        except Exception as e:
            logger.error(f"解析HTML资源失败: {str(e)}")

        return resources

    def _merge_resources(self, network_resources: Dict[str, List], html_resources: Dict[str, List]) -> Dict[str, List]:
        """合并网络请求和HTML解析的资源"""
        merged = {
            'scripts': [],
            'styles': [],
            'images': [],
            'other': []
        }

        seen_urls = set()

        # 合并每种类型的资源
        for resource_type in merged.keys():
            # 先添加网络请求中的资源
            for resource in network_resources.get(resource_type, []):
                url = resource['url']
                if url not in seen_urls:
                    seen_urls.add(url)
                    merged[resource_type].append(resource)

            # 再添加HTML解析的资源
            for resource in html_resources.get(resource_type, []):
                url = resource['url']
                if url not in seen_urls:
                    seen_urls.add(url)
                    merged[resource_type].append(resource)

        total_resources = sum(len(resources) for resources in merged.values())
        logger.info(f"合并后总共 {total_resources} 个资源文件")

        return merged
    
    def _fetch_with_browser(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用浏览器获取完整页面内容（包括JS生成的部分）"""
        results = []
        options = Options()
        options.headless = self.headless
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--ignore-certificate-errors")  # 忽略证书错误
        options.add_argument("--disable-extensions")  # 禁用扩展
        options.add_argument("--proxy-server='direct://'")  # 绕过系统代理
        options.add_argument("--proxy-bypass-list=*")  # 绕过所有代理
        options.add_argument("--disable-software-rasterizer")  # 禁用软件光栅化
        options.page_load_strategy = 'normal'  # 在此处正确设置页面加载策略
        
        try:
            # 启用性能日志
            options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
            
            # 自动管理Chrome驱动
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(
                service=service,
                options=options
            )
            driver.set_page_load_timeout(self.timeout)
            
            # 设置页面加载策略为"normal"确保完整加载
            # 正确设置页面加载策略的方法是在创建选项时设置
            # 删除 driver.set_page_load_strategy("normal") 调用
            
            # 启用网络跟踪
            driver.execute_cdp_cmd('Network.enable', {})
            
            for domain in domains:
                start_time = time.time()
                result = {
                    'domain': domain,
                    'success': False,
                    'source_code': '',
                    'resources': {},
                    'response_time': 0,
                    'status_code': 0,
                    'final_url': domain,
                    'content_length': 0,
                    'error': ''
                }
                
                try:
                    logger.info(f"正在访问: {domain}")
                    driver.get(domain)
                    
                    # 使用显式等待确保页面加载完成
                    WebDriverWait(driver, self.timeout).until(
                        lambda d: d.execute_script('return document.readyState') == 'complete'
                    )
                    
                    # 额外等待动态内容加载
                    time.sleep(1)
                    
                    # 获取渲染后的完整HTML
                    html_content = driver.page_source
                    final_url = driver.current_url
                    
                    # 捕获所有网络请求中的资源
                    resources = self._capture_network_requests(driver, domain)

                    # 解析HTML中的资源引用
                    html_resources = self._parse_html_resources(html_content, final_url)

                    # 合并资源
                    resources = self._merge_resources(resources, html_resources)
                    
                    result.update({
                        'success': True,
                        'source_code': html_content,
                        'resources': resources,
                        'response_time': time.time() - start_time,
                        'status_code': 200,  # 浏览器不提供状态码，假设成功为200
                        'final_url': final_url,
                        'content_length': len(html_content)
                    })
                    logger.info(f"成功获取 {domain} 的源码，包含 {len(resources['scripts'])} 个JS, {len(resources['styles'])} 个CSS, {len(resources['images'])} 个图片")
                    
                except TimeoutException:
                    error_msg = f"页面加载超时（{self.timeout}秒）"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"超时后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("超时后无法获取页面内容")
                
                except WebDriverException as e:
                    error_msg = f"浏览器错误: {str(e)}"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"错误后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("错误后无法获取页面内容")
                        
                except Exception as e:
                    error_msg = f"未知错误: {str(e)}"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"错误后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("错误后无法获取页面内容")
                
                # 无论成功与否，都将结果添加到列表中
                results.append(result)
            
            return results
            
        finally:
            driver.quit()  # 确保浏览器关闭

    def _fetch_with_browser_use(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用browser-use获取页面内容（更智能的浏览器自动化）"""
        results = []

        try:
            # 尝试导入browser-use，如果不可用则使用我们的包装器
            try:
                from browser_use import Browser
                logger.info("使用browser-use进行页面获取")
            except ImportError:
                from ..utils.browser_use_wrapper import Browser
                logger.info("使用browser-use包装器进行页面获取")
        except ImportError:
            logger.warning("browser-use和包装器都不可用，回退到selenium方法")
            return self._fetch_with_browser(domains)

        try:
            # 初始化browser-use
            browser = Browser(
                headless=self.headless,
                timeout=self.timeout
            )

            for domain in domains:
                start_time = time.time()
                result = {
                    'domain': domain,
                    'success': False,
                    'source_code': '',
                    'resources': {},
                    'response_time': 0,
                    'status_code': 0,
                    'final_url': domain,
                    'content_length': 0,
                    'error': ''
                }

                try:
                    logger.info(f"正在使用browser-use访问: {domain}")

                    # 使用browser-use访问页面
                    page_info = browser.get_page_info(domain)

                    if page_info.get('success', False):
                        html_content = page_info.get('html', '')
                        resources = page_info.get('resources', {})

                        result.update({
                            'success': True,
                            'source_code': html_content,
                            'resources': resources,
                            'response_time': time.time() - start_time,
                            'status_code': page_info.get('status_code', 200),
                            'final_url': page_info.get('final_url', domain),
                            'content_length': len(html_content)
                        })

                        logger.info(f"成功使用browser-use获取 {domain} 的源码")
                    else:
                        error_msg = page_info.get('error', '未知错误')
                        result.update({
                            'error': error_msg,
                            'response_time': time.time() - start_time
                        })
                        logger.error(f"browser-use获取 {domain} 失败: {error_msg}")

                except Exception as e:
                    error_msg = f"browser-use错误: {str(e)}"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")

                results.append(result)

            return results

        except Exception as e:
            logger.error(f"browser-use初始化失败: {str(e)}")
            # 回退到selenium方法
            return self._fetch_with_browser(domains)

        finally:
            try:
                browser.close()
            except:
                pass
