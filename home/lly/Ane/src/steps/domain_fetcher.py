#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版域名获取器 - 使用浏览器获取完整JS内容
"""

import logging
import os
import time
import json
import re
import html5lib
from typing import Dict, List, Any, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import urlparse, urljoin
from .base_step import BaseStep

# 配置日志
def setup_logger():
    logger = logging.getLogger('DomainFetcher')
    logger.setLevel(logging.INFO)
    
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 文件处理器
    file_handler = logging.FileHandler(os.path.join(log_dir, 'domain_fetcher.log'))
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 格式化
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

logger = setup_logger()

class DomainFetcher(BaseStep):
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        self.timeout = 60  # 增加超时时间
        self.headless = True  # 默认使用无头模式
        
    def validate_params(self, params: Dict[str, Any]) -> bool:
        if 'domains' not in params:
            return False
        domains = params['domains']
        return isinstance(domains, list) and len(domains) > 0
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要domains列表")
            
            domains = params['domains']
            method = params.get('method', 'browser')
            self.headless = params.get('headless', True)
            
            cleaned_domains = self._clean_domains(domains)
            logger.info(f"开始获取 {len(cleaned_domains)} 个域名的源码")
            
            results = self._fetch_with_browser(cleaned_domains)
            
            for result in results:
                self.add_result(result)
            
            success_count = len([r for r in results if r['success']])
            logger.info(f"成功获取 {success_count}/{len(domains)} 个域名的源码")
            
            self._complete_execution(True, f"成功获取 {success_count}/{len(domains)} 个域名的源码")
            
            return {
                'success': True,
                'message': f"成功获取 {success_count}/{len(domains)} 个域名的源码",
                'data': results,
                'total': len(domains),
                'success_count': success_count
            }
            
        except Exception as e:
            error_msg = f"执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': [],
                'total': 0,
                'success_count': 0
            }
    
    def _clean_domains(self, domains: List[str]) -> List[str]:
        cleaned = []
        for domain in domains:
            domain = domain.strip()
            if not domain:
                continue
            if not domain.startswith(('http://', 'https://')):
                domain = 'https://' + domain
            cleaned.append(domain)
        return list(set(cleaned))
    
    def _capture_network_requests(self, driver, domain):
        """使用浏览器开发者工具捕获所有网络请求"""
        resources = {
            'scripts': [],
            'styles': [],
            'images': [],
            'other': []
        }
        
        try:
            # 获取性能日志
            logs = driver.get_log('performance')
            seen_urls = set()  # 避免重复记录
            
            for entry in logs:
                try:
                    message = json.loads(entry['message'])['message']
                    
                    # 只处理请求已完成的事件
                    if message['method'] == 'Network.responseReceived':
                        response = message['params']['response']
                        url = response['url']
                        resource_type = response.get('type', 'other')
                        
                        # 过滤掉非资源类型的请求
                        if resource_type not in ['script', 'stylesheet', 'image']:
                            continue
                        
                        # 只处理同源资源
                        if url.startswith(('http://', 'https://')) and domain in url and url not in seen_urls:
                            seen_urls.add(url)
                            filename = os.path.basename(urlparse(url).path)
                            
                            if resource_type == 'script':
                                resources['scripts'].append({
                                    'url': url,
                                    'filename': filename or 'script.js',
                                    'type': 'js'
                                })
                            elif resource_type == 'stylesheet':
                                resources['styles'].append({
                                    'url': url,
                                    'filename': filename or 'style.css',
                                    'type': 'css'
                                })
                            elif resource_type == 'image':
                                resources['images'].append({
                                    'url': url,
                                    'filename': filename or 'image.png',
                                    'type': 'image'
                                })
                except Exception as e:
                    logger.debug(f"解析日志条目失败: {str(e)}")
                    
            logger.info(f"捕获到 {len(resources['scripts'])} 个JS, {len(resources['styles'])} 个CSS, {len(resources['images'])} 个图片")
        
        except Exception as e:
            logger.error(f"捕获网络请求失败: {str(e)}")
        
        return resources
    
    def _fetch_with_browser(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用浏览器获取完整页面内容（包括JS生成的部分）"""
        results = []
        options = Options()
        options.headless = self.headless
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--ignore-certificate-errors")  # 忽略证书错误
        options.add_argument("--disable-extensions")  # 禁用扩展
        options.add_argument("--proxy-server='direct://'")  # 绕过系统代理
        options.add_argument("--proxy-bypass-list=*")  # 绕过所有代理
        options.add_argument("--disable-software-rasterizer")  # 禁用软件光栅化
        options.page_load_strategy = 'normal'  # 在此处正确设置页面加载策略
        
        try:
            # 启用性能日志
            options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
            
            # 自动管理Chrome驱动
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(
                service=service,
                options=options
            )
            driver.set_page_load_timeout(self.timeout)
            
            # 设置页面加载策略为"normal"确保完整加载
            # 正确设置页面加载策略的方法是在创建选项时设置
            # 删除 driver.set_page_load_strategy("normal") 调用
            
            # 启用网络跟踪
            driver.execute_cdp_cmd('Network.enable', {})
            
            for domain in domains:
                start_time = time.time()
                result = {
                    'domain': domain,
                    'success': False,
                    'source_code': '',
                    'resources': {},
                    'response_time': 0,
                    'status_code': 0,
                    'final_url': domain,
                    'content_length': 0,
                    'error': ''
                }
                
                try:
                    logger.info(f"正在访问: {domain}")
                    driver.get(domain)
                    
                    # 使用显式等待确保页面加载完成
                    WebDriverWait(driver, self.timeout).until(
                        lambda d: d.execute_script('return document.readyState') == 'complete'
                    )
                    
                    # 额外等待动态内容加载
                    time.sleep(1)
                    
                    # 获取渲染后的完整HTML
                    html_content = driver.page_source
                    final_url = driver.current_url
                    
                    # 捕获所有网络请求中的资源
                    resources = self._capture_network_requests(driver, domain)
                    
                    result.update({
                        'success': True,
                        'source_code': html_content,
                        'resources': resources,
                        'response_time': time.time() - start_time,
                        'status_code': 200,  # 浏览器不提供状态码，假设成功为200
                        'final_url': final_url,
                        'content_length': len(html_content)
                    })
                    logger.info(f"成功获取 {domain} 的源码，包含 {len(resources['scripts'])} 个JS, {len(resources['styles'])} 个CSS, {len(resources['images'])} 个图片")
                    
                except TimeoutException:
                    error_msg = f"页面加载超时（{self.timeout}秒）"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"超时后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("超时后无法获取页面内容")
                
                except WebDriverException as e:
                    error_msg = f"浏览器错误: {str(e)}"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"错误后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("错误后无法获取页面内容")
                        
                except Exception as e:
                    error_msg = f"未知错误: {str(e)}"
                    result.update({
                        'error': error_msg,
                        'response_time': time.time() - start_time
                    })
                    logger.error(f"获取 {domain} 失败: {error_msg}")
                    # 尝试获取当前页面内容
                    try:
                        result['source_code'] = driver.page_source
                        result['final_url'] = driver.current_url
                        logger.warning(f"错误后仍获取到部分内容，长度: {len(result['source_code'])}")
                    except Exception:
                        logger.error("错误后无法获取页面内容")
                
                # 无论成功与否，都将结果添加到列表中
                results.append(result)
            
            return results
            
        finally:
            driver.quit()  # 确保浏览器关闭
