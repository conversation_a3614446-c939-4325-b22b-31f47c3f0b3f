#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息提取器 - 从页面源码中提取敏感信息
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Set
from .base_step import BaseStep

logger = logging.getLogger(__name__)

class InfoExtractor(BaseStep):
    """信息提取器 - 从页面源码中提取各种敏感信息"""
    
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        
        # 正则表达式模式
        self.patterns = {
            # 身份证号码
            'sfz': [
                r'[\'"](\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3})[\'"]',  # 15位身份证
                r'[\'"](\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x))[\'"]'  # 18位身份证
            ],
            
            # 手机号码
            'mobile': [
                r'[\'"]1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7}[\'"]'
            ],
            
            # 邮箱地址
            'mail': [
                r'[\'"][a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.((?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,})[\'"]'
            ],
            
            # IP地址
            'ip': [
                r'[\'"](?:([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?:\/.*?)?[\'"]'
            ],
            
            # IP:端口
            'ip_port': [
                r'[\'"](?:([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}(?:\/.*?)?[\'"]'
            ],
            
            # 域名
            'domain': [
                r'[\'"](?:([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(?::\d{1,5})?(?:\/)?[\'"]'
            ],
            
            # 路径
            'path': [
                r'[\'"](?:\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\]([^\>\< \)\(\{\}\,\'\"\\])*?[\'"]'
            ],
            
            # 不完整路径
            'incomplete_path': [
                r'[\'"][^\/\>\< \)\(\{\}\,\'\"\\][\w\/]*?\/[\w\/]*?[\'"]'
            ],
            
            # URL
            'url': [
                r'[\'"](?:([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(?::\d{1,5})?(?:\/.*?)?[\'"]'
            ],
            
            # JWT Token
            'jwt': [
                r'[\'"]ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}[\'"]',
                r'[\'"]ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,}[\'"]'
            ],
            
            # 加密算法
            'algorithm': [
                r'\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|\$\.md5|md5|sha1|sha256|sha512)[\(\.]'
            ]
        }
        
        # 密钥相关的关键词
        self.secret_keywords = [
            'secret', 'key', 'token', 'password', 'pwd', 'pass', 'auth',
            'api_key', 'apikey', 'access_token', 'refresh_token', 'session',
            'salt', 'iv', 'cipher', 'encrypt', 'decrypt', 'private_key',
            'public_key', 'certificate', 'cert', 'signature', 'sign'
        ]
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return 'source_code' in params or 'content' in params
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行信息提取"""
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要source_code或content")
            
            source_code = params.get('source_code') or params.get('content', '')
            filename = params.get('filename', 'unknown')
            
            logger.info(f"开始提取信息，源码长度: {len(source_code)}")
            
            # 执行信息提取
            extracted_info = self._extract_info(source_code)
            
            # 保存结果
            result = {
                'filename': filename,
                'source_length': len(source_code),
                'extracted_info': extracted_info,
                'summary': self._generate_summary(extracted_info)
            }
            
            self.add_result(result)
            
            total_items = sum(len(items) for items in extracted_info.values() if items)
            message = f'成功提取信息: 共发现 {total_items} 项敏感信息'
            self._complete_execution(True, message)
            
            return {
                'success': True,
                'message': message,
                'data': result,
                'filename': filename
            }
            
        except Exception as e:
            error_msg = f'信息提取失败: {str(e)}'
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {},
                'filename': params.get('filename', 'unknown')
            }
    
    def _extract_info(self, data: str) -> Dict[str, List[str]]:
        """提取各种信息"""
        extract_data = {}
        
        # 提取各种模式的信息
        for info_type, patterns in self.patterns.items():
            matches = set()  # 使用set去重
            
            for pattern in patterns:
                try:
                    found = re.findall(pattern, data, re.IGNORECASE)
                    if found:
                        # 处理不同的匹配结果格式
                        for match in found:
                            if isinstance(match, tuple):
                                # 如果是元组，取第一个非空元素
                                for item in match:
                                    if item:
                                        matches.add(item)
                                        break
                            else:
                                matches.add(match)
                except re.error as e:
                    logger.warning(f"正则表达式错误 {pattern}: {str(e)}")
                    continue
            
            extract_data[info_type] = list(matches)
        
        # 提取密钥信息
        extract_data['secret'] = self._get_secret(data)
        
        # 从URL中提取额外的IP和域名信息
        if extract_data.get('url'):
            self._extract_from_urls(extract_data)
        
        # 清理和验证结果
        extract_data = self._clean_and_validate(extract_data)
        
        return extract_data
    
    def _get_secret(self, data: str) -> List[str]:
        """提取可能的密钥信息"""
        secrets = set()
        
        # 查找包含密钥关键词的赋值语句
        for keyword in self.secret_keywords:
            patterns = [
                rf'{keyword}\s*[:=]\s*[\'"]([^\'\"]+)[\'"]',
                rf'[\'\"]{keyword}[\'\"]\s*[:=]\s*[\'"]([^\'\"]+)[\'"]',
                rf'{keyword}[\'\"]\s*:\s*[\'"]([^\'\"]+)[\'"]'
            ]
            
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, data, re.IGNORECASE)
                    for match in matches:
                        if len(match) > 8:  # 只保留长度大于8的可能密钥
                            secrets.add(match)
                except re.error:
                    continue
        
        # 查找可能的API密钥格式
        api_key_patterns = [
            r'[\'"]([A-Za-z0-9]{32,})[\'"]',  # 32位以上的字母数字组合
            r'[\'"]([A-Za-z0-9_-]{20,})[\'"]',  # 20位以上包含下划线和连字符
            r'[\'"]sk-[A-Za-z0-9]{20,}[\'"]',  # OpenAI风格的密钥
            r'[\'"]pk_[A-Za-z0-9]{20,}[\'"]',  # Stripe风格的公钥
        ]
        
        for pattern in api_key_patterns:
            try:
                matches = re.findall(pattern, data)
                for match in matches:
                    if len(match) >= 20:
                        secrets.add(match)
            except re.error:
                continue
        
        return list(secrets)
    
    def _extract_from_urls(self, extract_data: Dict[str, List[str]]):
        """从URL中提取额外的IP和域名信息"""
        for url in extract_data['url']:
            # 从URL中提取IP
            ip_matches = re.findall(r'(?:([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', url)
            if ip_matches:
                extract_data['ip'].extend(ip_matches)
            
            # 从URL中提取IP:端口
            ip_port_matches = re.findall(r'(?:([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}(?:\/.*?)?', url)
            if ip_port_matches:
                extract_data['ip_port'].extend(ip_port_matches)
            
            # 从URL中提取域名
            domain_matches = re.findall(r'(?:([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(?::\d{1,5})?', url)
            if domain_matches:
                extract_data['domain'].extend([match[0] if isinstance(match, tuple) else match for match in domain_matches])
    
    def _clean_and_validate(self, extract_data: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """清理和验证提取的数据"""
        cleaned_data = {}
        
        for key, values in extract_data.items():
            if not values:
                cleaned_data[key] = []
                continue
            
            # 去重并过滤
            unique_values = list(set(values))
            
            # 根据类型进行特定的清理
            if key == 'sfz':
                # 验证身份证号码格式
                unique_values = [v for v in unique_values if self._validate_id_card(v)]
            elif key == 'mobile':
                # 验证手机号码格式
                unique_values = [v for v in unique_values if len(v) == 11 and v.startswith('1')]
            elif key == 'mail':
                # 简单的邮箱验证
                unique_values = [v for v in unique_values if '@' in v and '.' in v]
            elif key == 'ip':
                # 验证IP地址格式
                unique_values = [v for v in unique_values if self._validate_ip(v)]
            
            # 移除引号
            cleaned_values = []
            for value in unique_values:
                cleaned_value = value.strip('\'"')
                if cleaned_value and len(cleaned_value) > 1:
                    cleaned_values.append(cleaned_value)
            
            cleaned_data[key] = cleaned_values
        
        return cleaned_data
    
    def _validate_id_card(self, id_card: str) -> bool:
        """验证身份证号码"""
        if len(id_card) not in [15, 18]:
            return False
        
        if len(id_card) == 15:
            return id_card.isdigit()
        else:
            return id_card[:-1].isdigit() and id_card[-1] in '0123456789Xx'
    
    def _validate_ip(self, ip: str) -> bool:
        """验证IP地址"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            
            for part in parts:
                if not part.isdigit() or not 0 <= int(part) <= 255:
                    return False
            
            return True
        except:
            return False
    
    def _generate_summary(self, extracted_info: Dict[str, List[str]]) -> Dict[str, int]:
        """生成提取结果摘要"""
        summary = {}
        
        type_names = {
            'sfz': '身份证号',
            'mobile': '手机号码',
            'mail': '邮箱地址',
            'ip': 'IP地址',
            'ip_port': 'IP:端口',
            'domain': '域名',
            'path': '路径',
            'incomplete_path': '不完整路径',
            'url': 'URL',
            'jwt': 'JWT Token',
            'algorithm': '加密算法',
            'secret': '可能的密钥'
        }
        
        for key, values in extracted_info.items():
            if values:
                display_name = type_names.get(key, key)
                summary[display_name] = len(values)
        
        return summary
