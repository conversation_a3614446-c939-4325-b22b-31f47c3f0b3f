#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漏洞审计器 - 使用AI对代码进行安全审计
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from .base_step import BaseStep

logger = logging.getLogger(__name__)

class VulnerabilityAudit(BaseStep):
    """漏洞审计器 - 使用AI模型进行代码安全审计"""
    
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        
        # 漏洞类型定义
        self.vulnerability_types = {
            'XSS': '跨站脚本攻击',
            'CSRF': '跨站请求伪造',
            'SQL_INJECTION': 'SQL注入',
            'CODE_INJECTION': '代码注入',
            'PATH_TRAVERSAL': '路径遍历',
            'SENSITIVE_DATA': '敏感数据泄露',
            'WEAK_CRYPTO': '弱加密',
            'INSECURE_STORAGE': '不安全存储',
            'AUTHENTICATION': '认证缺陷',
            'AUTHORIZATION': '授权缺陷',
            'INPUT_VALIDATION': '输入验证不足',
            'OUTPUT_ENCODING': '输出编码不当'
        }
        
        # 审计提示词模板
        self.audit_prompts = {
            'frontend': """
你是一个专业的前端安全专家。请对以下前端代码进行全面的安全审计。

代码文件: {filename}
代码内容:
```{language}
{code}
```

请重点关注以下安全问题：
1. XSS (跨站脚本攻击) - innerHTML, dangerouslySetInnerHTML等
2. CSRF (跨站请求伪造) - 缺少CSRF令牌
3. 敏感数据泄露 - API密钥、密码等硬编码
4. 输入验证不足 - 用户输入未经验证
5. 不安全的第三方依赖
6. 客户端存储安全 - localStorage, sessionStorage
7. URL重定向漏洞
8. 点击劫持防护
9. 内容安全策略(CSP)缺失
10. 其他前端安全问题

请以JSON格式返回审计结果：
{{
  "summary": {{
    "total_issues": "发现的问题总数",
    "high_risk": "高风险问题数",
    "medium_risk": "中风险问题数",
    "low_risk": "低风险问题数",
    "overall_score": "安全评分(1-10)"
  }},
  "vulnerabilities": [
    {{
      "type": "漏洞类型",
      "severity": "严重程度(HIGH/MEDIUM/LOW)",
      "title": "漏洞标题",
      "description": "详细描述",
      "location": {{
        "line": "行号",
        "code": "问题代码片段"
      }},
      "impact": "潜在影响",
      "recommendation": "修复建议",
      "cwe_id": "CWE编号(如果适用)"
    }}
  ],
  "best_practices": [
    "安全最佳实践建议"
  ]
}}
""",
            'backend': """
你是一个专业的后端安全专家。请对以下后端代码进行全面的安全审计。

代码文件: {filename}
代码内容:
```{language}
{code}
```

请重点关注以下安全问题：
1. SQL注入
2. 命令注入
3. 路径遍历
4. 认证和授权缺陷
5. 会话管理问题
6. 加密和哈希问题
7. 输入验证和输出编码
8. 错误处理和信息泄露
9. 不安全的反序列化
10. 其他后端安全问题

请以JSON格式返回审计结果：
{{
  "summary": {{
    "total_issues": "发现的问题总数",
    "high_risk": "高风险问题数",
    "medium_risk": "中风险问题数",
    "low_risk": "低风险问题数",
    "overall_score": "安全评分(1-10)"
  }},
  "vulnerabilities": [
    {{
      "type": "漏洞类型",
      "severity": "严重程度(HIGH/MEDIUM/LOW)",
      "title": "漏洞标题",
      "description": "详细描述",
      "location": {{
        "line": "行号",
        "code": "问题代码片段"
      }},
      "impact": "潜在影响",
      "recommendation": "修复建议",
      "cwe_id": "CWE编号(如果适用)"
    }}
  ],
  "best_practices": [
    "安全最佳实践建议"
  ]
}}
"""
        }
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return ('code' in params or 'file_content' in params) and 'ai_model' in params
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行漏洞审计"""
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要code/file_content和ai_model")
            
            code = params.get('code') or params.get('file_content', '')
            filename = params.get('filename', 'unknown')
            ai_model = params.get('ai_model', 'deepseek')
            api_key = params.get('api_key', '')
            
            if not api_key:
                raise ValueError("需要提供AI模型的API密钥")
            
            logger.info(f"开始审计代码文件: {filename}，使用模型: {ai_model}")
            
            # 检测代码类型
            code_type = self._detect_code_type(code, filename)
            language = self._get_language(filename)
            
            # 执行审计
            audit_result = self._audit_code(code, filename, language, code_type, ai_model, api_key)
            
            # 保存结果
            result = {
                'filename': filename,
                'code_type': code_type,
                'language': language,
                'ai_model': ai_model,
                'audit_result': audit_result,
                'success': True
            }
            
            self.add_result(result)
            
            total_issues = audit_result.get('summary', {}).get('total_issues', 0)
            message = f'成功审计代码文件: {filename}，发现 {total_issues} 个安全问题'
            self._complete_execution(True, message)
            
            return {
                'success': True,
                'message': message,
                'data': result,
                'filename': filename
            }
            
        except Exception as e:
            error_msg = f'漏洞审计失败: {str(e)}'
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {},
                'filename': params.get('filename', 'unknown')
            }
    
    def _detect_code_type(self, code: str, filename: str) -> str:
        """检测代码类型（前端/后端）"""
        # 前端特征
        frontend_indicators = [
            'document.', 'window.', 'localStorage', 'sessionStorage',
            'React', 'Vue', 'Angular', 'jQuery', 'DOM', 'getElementById',
            'addEventListener', 'innerHTML', 'dangerouslySetInnerHTML'
        ]
        
        # 后端特征
        backend_indicators = [
            'require(', 'import ', 'express', 'app.get', 'app.post',
            'database', 'sql', 'query', 'connection', 'server',
            'middleware', 'authentication', 'session'
        ]
        
        frontend_score = sum(1 for indicator in frontend_indicators if indicator in code)
        backend_score = sum(1 for indicator in backend_indicators if indicator in code)
        
        return 'frontend' if frontend_score >= backend_score else 'backend'
    
    def _get_language(self, filename: str) -> str:
        """根据文件名获取编程语言"""
        if filename.endswith(('.js', '.jsx')):
            return 'javascript'
        elif filename.endswith(('.ts', '.tsx')):
            return 'typescript'
        elif filename.endswith('.vue'):
            return 'vue'
        elif filename.endswith('.py'):
            return 'python'
        elif filename.endswith('.java'):
            return 'java'
        elif filename.endswith('.php'):
            return 'php'
        elif filename.endswith('.html'):
            return 'html'
        elif filename.endswith('.css'):
            return 'css'
        else:
            return 'text'
    
    def _audit_code(self, code: str, filename: str, language: str, code_type: str, ai_model: str, api_key: str) -> Dict[str, Any]:
        """使用AI模型审计代码"""
        # 获取提示词
        prompt_template = self.audit_prompts.get(code_type, self.audit_prompts['frontend'])
        prompt = prompt_template.format(
            filename=filename,
            language=language,
            code=code[:8000]  # 限制代码长度
        )
        
        # 调用AI模型
        response = self._call_ai_model(prompt, ai_model, api_key)
        
        # 解析响应
        try:
            result = json.loads(response)
            return result
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    return result
                except json.JSONDecodeError:
                    pass
            
            # 如果仍然失败，返回原始响应
            return {
                'raw_response': response,
                'error': 'Failed to parse JSON response',
                'summary': {'total_issues': 0, 'overall_score': 0},
                'vulnerabilities': []
            }
    
    def _call_ai_model(self, prompt: str, model_name: str, api_key: str) -> str:
        """调用AI模型API"""
        import requests
        
        if model_name == 'deepseek':
            return self._call_deepseek(prompt, api_key)
        elif model_name == 'minimax':
            return self._call_minimax(prompt, api_key)
        else:
            raise ValueError(f"不支持的AI模型: {model_name}")
    
    def _call_deepseek(self, prompt: str, api_key: str) -> str:
        """调用DeepSeek API"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'deepseek-coder',
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 4000,
            'temperature': 0.1
        }
        
        try:
            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
        
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API调用失败: {str(e)}")
            raise Exception(f"DeepSeek API调用失败: {str(e)}")
    
    def _call_minimax(self, prompt: str, api_key: str) -> str:
        """调用MiniMax API"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': 'abab6.5-chat',
            'messages': [
                {
                    'sender_type': 'USER',
                    'text': prompt
                }
            ],
            'tokens_to_generate': 4000,
            'temperature': 0.1
        }
        
        try:
            response = requests.post(
                'https://api.minimax.chat/v1/text/chatcompletion_pro',
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result['reply']
        
        except requests.exceptions.RequestException as e:
            logger.error(f"MiniMax API调用失败: {str(e)}")
            raise Exception(f"MiniMax API调用失败: {str(e)}")
    
    def get_vulnerability_summary(self, audit_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取漏洞摘要"""
        summary = audit_result.get('summary', {})
        vulnerabilities = audit_result.get('vulnerabilities', [])
        
        # 按严重程度分类
        severity_counts = {'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'LOW')
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        return {
            'total_issues': len(vulnerabilities),
            'high_risk': severity_counts['HIGH'],
            'medium_risk': severity_counts['MEDIUM'],
            'low_risk': severity_counts['LOW'],
            'overall_score': summary.get('overall_score', 0),
            'top_vulnerabilities': vulnerabilities[:5]  # 前5个最重要的漏洞
        }
