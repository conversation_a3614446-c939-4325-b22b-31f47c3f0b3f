#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码拆分器 - 使用AI将打包后的代码拆分为原始文件
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from .base_step import BaseStep

logger = logging.getLogger(__name__)

class CodeSplitter(BaseStep):
    """代码拆分器 - 使用AI模型拆分webpack等打包后的代码"""
    
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        
        # AI模型配置
        self.ai_models = {
            'deepseek': {
                'api_url': 'https://api.deepseek.com/v1/chat/completions',
                'model': 'deepseek-coder',
                'max_tokens': 4000
            },
            'minimax': {
                'api_url': 'https://api.minimax.chat/v1/text/chatcompletion_pro',
                'model': 'abab6.5-chat',
                'max_tokens': 4000
            }
        }
        
        # 拆分提示词模板
        self.split_prompts = {
            'webpack': """
你是一个专业的前端代码分析师。请分析以下webpack打包后的JavaScript代码，将其拆分为原始的模块文件。

代码内容：
```javascript
{code}
```

请按照以下要求进行拆分：
1. 识别webpack模块边界
2. 提取每个模块的原始代码
3. 推断模块的文件名和路径
4. 保持代码的完整性和可读性

请以JSON格式返回结果：
{{
  "modules": [
    {{
      "filename": "推断的文件名",
      "path": "推断的文件路径",
      "content": "模块的原始代码",
      "type": "文件类型(js/vue/jsx等)",
      "dependencies": ["依赖的其他模块"]
    }}
  ],
  "analysis": {{
    "total_modules": "模块总数",
    "main_framework": "主要使用的框架",
    "build_tool": "构建工具"
  }}
}}
""",
            'vue': """
你是一个Vue.js专家。请分析以下代码，将其拆分为独立的Vue组件文件。

代码内容：
```javascript
{code}
```

请按照以下要求进行拆分：
1. 识别Vue组件定义
2. 提取template、script、style部分
3. 推断组件名称和文件名
4. 保持Vue单文件组件格式

请以JSON格式返回结果：
{{
  "components": [
    {{
      "filename": "组件文件名.vue",
      "component_name": "组件名称",
      "template": "模板部分代码",
      "script": "脚本部分代码",
      "style": "样式部分代码",
      "props": ["组件属性"],
      "dependencies": ["依赖的其他组件"]
    }}
  ],
  "analysis": {{
    "total_components": "组件总数",
    "vue_version": "Vue版本",
    "ui_framework": "UI框架"
  }}
}}
""",
            'react': """
你是一个React专家。请分析以下代码，将其拆分为独立的React组件文件。

代码内容：
```javascript
{code}
```

请按照以下要求进行拆分：
1. 识别React组件定义
2. 提取JSX和逻辑代码
3. 推断组件名称和文件名
4. 保持React组件格式

请以JSON格式返回结果：
{{
  "components": [
    {{
      "filename": "组件文件名.jsx",
      "component_name": "组件名称",
      "content": "完整的组件代码",
      "imports": ["导入的依赖"],
      "exports": ["导出的内容"],
      "hooks": ["使用的React Hooks"],
      "dependencies": ["依赖的其他组件"]
    }}
  ],
  "analysis": {{
    "total_components": "组件总数",
    "react_version": "React版本",
    "ui_framework": "UI框架"
  }}
}}
"""
        }
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return ('code' in params or 'file_content' in params) and 'ai_model' in params
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行代码拆分"""
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要code/file_content和ai_model")
            
            code = params.get('code') or params.get('file_content', '')
            filename = params.get('filename', 'unknown')
            ai_model = params.get('ai_model', 'deepseek')
            api_key = params.get('api_key', '')
            
            if not api_key:
                raise ValueError("需要提供AI模型的API密钥")
            
            logger.info(f"开始拆分代码文件: {filename}，使用模型: {ai_model}")
            
            # 检测代码类型
            code_type = self._detect_code_type(code, filename)
            
            # 执行拆分
            split_result = self._split_code(code, code_type, ai_model, api_key)
            
            # 保存结果
            result = {
                'filename': filename,
                'code_type': code_type,
                'ai_model': ai_model,
                'split_result': split_result,
                'success': True
            }
            
            self.add_result(result)
            
            message = f'成功拆分代码文件: {filename}，生成 {len(split_result.get("modules", split_result.get("components", [])))} 个文件'
            self._complete_execution(True, message)
            
            return {
                'success': True,
                'message': message,
                'data': result,
                'filename': filename
            }
            
        except Exception as e:
            error_msg = f'代码拆分失败: {str(e)}'
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {},
                'filename': params.get('filename', 'unknown')
            }
    
    def _detect_code_type(self, code: str, filename: str) -> str:
        """检测代码类型以选择合适的拆分策略"""
        # 检查webpack特征
        if '__webpack_require__' in code or 'webpackJsonp' in code:
            return 'webpack'
        
        # 检查Vue特征
        if 'Vue.component' in code or 'new Vue(' in code or '.vue' in filename:
            return 'vue'
        
        # 检查React特征
        if 'React.createElement' in code or 'jsx' in filename or 'tsx' in filename:
            return 'react'
        
        # 默认使用webpack策略
        return 'webpack'
    
    def _split_code(self, code: str, code_type: str, ai_model: str, api_key: str) -> Dict[str, Any]:
        """使用AI模型拆分代码"""
        # 获取提示词
        prompt_template = self.split_prompts.get(code_type, self.split_prompts['webpack'])
        prompt = prompt_template.format(code=code[:8000])  # 限制代码长度
        
        # 调用AI模型
        response = self._call_ai_model(prompt, ai_model, api_key)
        
        # 解析响应
        try:
            result = json.loads(response)
            return result
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    return result
                except json.JSONDecodeError:
                    pass
            
            # 如果仍然失败，返回原始响应
            return {
                'raw_response': response,
                'error': 'Failed to parse JSON response',
                'modules': []
            }
    
    def _call_ai_model(self, prompt: str, model_name: str, api_key: str) -> str:
        """调用AI模型API"""
        import requests
        
        model_config = self.ai_models.get(model_name)
        if not model_config:
            raise ValueError(f"不支持的AI模型: {model_name}")
        
        if model_name == 'deepseek':
            return self._call_deepseek(prompt, api_key, model_config)
        elif model_name == 'minimax':
            return self._call_minimax(prompt, api_key, model_config)
        else:
            raise ValueError(f"未实现的AI模型: {model_name}")
    
    def _call_deepseek(self, prompt: str, api_key: str, config: Dict[str, Any]) -> str:
        """调用DeepSeek API"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': config['model'],
            'messages': [
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': config['max_tokens'],
            'temperature': 0.1
        }
        
        try:
            response = requests.post(config['api_url'], headers=headers, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            return result['choices'][0]['message']['content']
        
        except requests.exceptions.RequestException as e:
            logger.error(f"DeepSeek API调用失败: {str(e)}")
            raise Exception(f"DeepSeek API调用失败: {str(e)}")
    
    def _call_minimax(self, prompt: str, api_key: str, config: Dict[str, Any]) -> str:
        """调用MiniMax API"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': config['model'],
            'messages': [
                {
                    'sender_type': 'USER',
                    'text': prompt
                }
            ],
            'tokens_to_generate': config['max_tokens'],
            'temperature': 0.1
        }
        
        try:
            response = requests.post(config['api_url'], headers=headers, json=data, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            return result['reply']
        
        except requests.exceptions.RequestException as e:
            logger.error(f"MiniMax API调用失败: {str(e)}")
            raise Exception(f"MiniMax API调用失败: {str(e)}")
    
    def get_split_files(self, split_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从拆分结果中提取文件列表"""
        files = []
        
        # 处理webpack模块
        if 'modules' in split_result:
            for module in split_result['modules']:
                files.append({
                    'filename': module.get('filename', 'unknown.js'),
                    'path': module.get('path', '/'),
                    'content': module.get('content', ''),
                    'type': module.get('type', 'js'),
                    'dependencies': module.get('dependencies', [])
                })
        
        # 处理Vue组件
        elif 'components' in split_result:
            for component in split_result['components']:
                # 构建Vue单文件组件内容
                vue_content = self._build_vue_file(component)
                files.append({
                    'filename': component.get('filename', 'Component.vue'),
                    'path': '/components/',
                    'content': vue_content,
                    'type': 'vue',
                    'component_name': component.get('component_name', ''),
                    'dependencies': component.get('dependencies', [])
                })
        
        return files
    
    def _build_vue_file(self, component: Dict[str, Any]) -> str:
        """构建Vue单文件组件内容"""
        template = component.get('template', '')
        script = component.get('script', '')
        style = component.get('style', '')
        
        vue_content = ""
        
        if template:
            vue_content += f"<template>\n{template}\n</template>\n\n"
        
        if script:
            vue_content += f"<script>\n{script}\n</script>\n\n"
        
        if style:
            vue_content += f"<style>\n{style}\n</style>\n"
        
        return vue_content
