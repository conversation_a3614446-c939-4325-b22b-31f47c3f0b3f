#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版步骤基类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any
from enum import Enum
from datetime import datetime

class StepStatus(Enum):
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"

class BaseStep(ABC):
    def __init__(self, step_id: str, name: str, description: str):
        self.step_id = step_id
        self.name = name
        self.description = description
        self.status = StepStatus.READY
        self.results: List[Dict[str, Any]] = []
        self.error_message: str = ""
        self.start_time: datetime = None
        self.end_time: datetime = None
    
    @abstractmethod
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def validate_params(self, params: Dict[str, Any]) -> bool:
        pass
    
    def can_run(self) -> bool:
        return self.status in [StepStatus.READY, StepStatus.COMPLETED]
    
    def reset(self):
        self.status = StepStatus.READY
        self.results.clear()
        self.error_message = ""
        self.start_time = None
        self.end_time = None
    
    def clear_results(self):
        self.results.clear()
    
    def add_result(self, result: Dict[str, Any]):
        result['timestamp'] = datetime.now().isoformat()
        self.results.append(result)
    
    def get_execution_time(self) -> float:
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
    
    def _start_execution(self):
        self.status = StepStatus.RUNNING
        self.start_time = datetime.now()
        self.error_message = ""
        print(f"步骤 {self.name} 开始执行")
    
    def _complete_execution(self, success: bool = True, message: str = ""):
        self.end_time = datetime.now()
        if success:
            self.status = StepStatus.COMPLETED
            print(f"步骤 {self.name} 执行完成，耗时 {self.get_execution_time():.2f}秒")
        else:
            self.status = StepStatus.ERROR
            self.error_message = message
            print(f"步骤 {self.name} 执行失败: {message}")

