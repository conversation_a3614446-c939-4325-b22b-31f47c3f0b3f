# Simple framework analyzer
from .base_step import BaseStep
from typing import Dict, List, Any

class FrameworkAnalyzer(BaseStep):
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return 'source_data' in params
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        self._start_execution()
        try:
            results = []
            for data in params['source_data']:
                if data.get('success'):
                    result = {
                        'domain': data['domain'],
                        'frameworks': {'detected': True},
                        'has_sourcemap': 'sourceMappingURL' in data.get('source_code', '')
                    }
                    results.append(result)
                    self.add_result(result)
            
            message = f'成功分析 {len(results)} 个网页'
            self._complete_execution(True, message)
            return {
                'success': True, 
                'message': message,
                'data': results, 
                'analyzed_count': len(results)
            }
        except Exception as e:
            error_msg = f'分析失败: {str(e)}'
            self._complete_execution(False, error_msg)
            return {
                'success': False, 
                'message': error_msg, 
                'data': []
            }

