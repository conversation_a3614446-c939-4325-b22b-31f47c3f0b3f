#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端框架分析器 - 增强版
"""

import re
import json
import logging
from typing import Dict, List, Any, Set
from .base_step import BaseStep

logger = logging.getLogger(__name__)

class FrameworkAnalyzer(BaseStep):
    """前端框架分析器"""

    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)

        # 框架检测规则
        self.framework_patterns = {
            'React': [
                r'react\.js',
                r'react\.min\.js',
                r'React\.createElement',
                r'ReactDOM\.render',
                r'__REACT_DEVTOOLS_GLOBAL_HOOK__',
                r'data-reactroot'
            ],
            'Vue': [
                r'vue\.js',
                r'vue\.min\.js',
                r'Vue\.component',
                r'new Vue\(',
                r'v-if=',
                r'v-for=',
                r'{{.*}}'
            ],
            'Angular': [
                r'angular\.js',
                r'angular\.min\.js',
                r'ng-app=',
                r'ng-controller=',
                r'angular\.module',
                r'\[ng-'
            ],
            'jQuery': [
                r'jquery\.js',
                r'jquery\.min\.js',
                r'\$\(',
                r'jQuery\('
            ],
            'Bootstrap': [
                r'bootstrap\.css',
                r'bootstrap\.min\.css',
                r'bootstrap\.js',
                r'bootstrap\.min\.js',
                r'class=".*\bbtn\b',
                r'class=".*\bcontainer\b'
            ],
            'Webpack': [
                r'webpack',
                r'__webpack_require__',
                r'webpackJsonp'
            ],
            'Next.js': [
                r'_next/',
                r'__NEXT_DATA__',
                r'next\.js'
            ],
            'Nuxt.js': [
                r'_nuxt/',
                r'__NUXT__',
                r'nuxt\.js'
            ]
        }

        # 打包工具检测规则
        self.bundler_patterns = {
            'webpack': [r'__webpack_require__', r'webpackJsonp', r'/\*\s*webpack\s*\*/'],
            'rollup': [r'rollup', r'createCommonjsModule'],
            'parcel': [r'parcelRequire', r'parcel-bundler'],
            'vite': [r'import\.meta\.hot', r'/@vite/'],
            'esbuild': [r'esbuild', r'__esm\(']
        }

    def validate_params(self, params: Dict[str, Any]) -> bool:
        return 'source_data' in params and isinstance(params['source_data'], list)

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行框架分析"""
        self._start_execution()

        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要source_data列表")

            source_data = params['source_data']
            results = []

            logger.info(f"开始分析 {len(source_data)} 个域名的前端框架")

            for data in source_data:
                if not data.get('success'):
                    continue

                domain = data['domain']
                source_code = data.get('source_code', '')
                resources = data.get('resources', {})

                logger.info(f"正在分析域名: {domain}")

                # 分析框架
                frameworks = self._detect_frameworks(source_code, resources)

                # 分析Source Map
                sourcemap_info = self._analyze_sourcemap(source_code, resources)

                # 分析打包工具
                bundler = self._detect_bundler(source_code, resources)

                result = {
                    'domain': domain,
                    'frameworks': frameworks,
                    'has_sourcemap': sourcemap_info['has_sourcemap'],
                    'sourcemap_urls': sourcemap_info['urls'],
                    'bundler': bundler
                }

                results.append(result)
                self.add_result(result)

                logger.info(f"完成分析 {domain}: 检测到 {len(frameworks)} 个框架")

            message = f'成功分析 {len(results)} 个网页'
            self._complete_execution(True, message)

            return {
                'success': True,
                'message': message,
                'data': results,
                'analyzed_count': len(results)
            }

        except Exception as e:
            error_msg = f'分析失败: {str(e)}'
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': [],
                'analyzed_count': 0
            }

    def _detect_frameworks(self, source_code: str, resources: Dict[str, List]) -> Dict[str, Dict[str, Any]]:
        """检测前端框架"""
        detected_frameworks = {}

        # 合并所有资源URL用于检测
        all_urls = []
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                all_urls.append(resource.get('url', ''))

        # 将所有内容合并用于模式匹配
        all_content = source_code + ' ' + ' '.join(all_urls)

        for framework, patterns in self.framework_patterns.items():
            confidence = 0
            matched_patterns = []

            for pattern in patterns:
                if re.search(pattern, all_content, re.IGNORECASE):
                    confidence += 20  # 每个匹配的模式增加20%置信度
                    matched_patterns.append(pattern)

            # 限制最大置信度为100%
            confidence = min(confidence, 100)

            if confidence > 0:
                detected_frameworks[framework] = {
                    'confidence': confidence,
                    'matched_patterns': matched_patterns,
                    'evidence': self._get_framework_evidence(framework, all_content)
                }

        return detected_frameworks

    def _get_framework_evidence(self, framework: str, content: str) -> List[str]:
        """获取框架检测的证据"""
        evidence = []

        if framework == 'React':
            if 'data-reactroot' in content:
                evidence.append('发现React根元素标识')
            if 'React.createElement' in content:
                evidence.append('发现React.createElement调用')
            if '__REACT_DEVTOOLS_GLOBAL_HOOK__' in content:
                evidence.append('发现React开发工具钩子')

        elif framework == 'Vue':
            if re.search(r'v-\w+', content):
                evidence.append('发现Vue指令')
            if '{{' in content and '}}' in content:
                evidence.append('发现Vue模板语法')
            if 'new Vue(' in content:
                evidence.append('发现Vue实例化')

        elif framework == 'Angular':
            if re.search(r'ng-\w+', content):
                evidence.append('发现Angular指令')
            if 'angular.module' in content:
                evidence.append('发现Angular模块定义')

        elif framework == 'jQuery':
            if '$(' in content or 'jQuery(' in content:
                evidence.append('发现jQuery选择器调用')

        return evidence

    def _analyze_sourcemap(self, source_code: str, resources: Dict[str, List]) -> Dict[str, Any]:
        """分析Source Map信息"""
        sourcemap_urls = set()

        # 在源码中查找sourceMappingURL
        sourcemap_pattern = r'//[@#]\s*sourceMappingURL=([^\s]+)'
        matches = re.findall(sourcemap_pattern, source_code)
        sourcemap_urls.update(matches)

        # 在资源文件中查找.map文件
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                url = resource.get('url', '')
                if url.endswith('.map'):
                    sourcemap_urls.add(url)
                # 检查对应的.map文件
                if url.endswith('.js'):
                    map_url = url + '.map'
                    sourcemap_urls.add(map_url)
                elif url.endswith('.css'):
                    map_url = url + '.map'
                    sourcemap_urls.add(map_url)

        return {
            'has_sourcemap': len(sourcemap_urls) > 0,
            'urls': list(sourcemap_urls)
        }

    def _detect_bundler(self, source_code: str, resources: Dict[str, List]) -> str:
        """检测打包工具"""
        # 合并所有内容用于检测
        all_urls = []
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                all_urls.append(resource.get('url', ''))

        all_content = source_code + ' ' + ' '.join(all_urls)

        for bundler, patterns in self.bundler_patterns.items():
            for pattern in patterns:
                if re.search(pattern, all_content, re.IGNORECASE):
                    return bundler

        return 'unknown'

