#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码分析器 - 分析代码使用的框架和构建工具
"""

import re
import json
import logging
from typing import Dict, List, Any, Set
from .base_step import BaseStep

logger = logging.getLogger(__name__)

class CodeAnalyzer(BaseStep):
    """代码分析器 - 深度分析代码结构和技术栈"""
    
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        
        # 框架检测规则 - 更详细的模式
        self.framework_patterns = {
            'React': {
                'patterns': [
                    r'import\s+React\s+from\s+[\'"]react[\'"]',
                    r'from\s+[\'"]react[\'"]',
                    r'React\.createElement',
                    r'ReactDOM\.render',
                    r'useState|useEffect|useContext',
                    r'jsx|\.jsx',
                    r'<[A-Z][a-zA-Z0-9]*[^>]*>',  # JSX组件
                    r'className=',
                    r'onClick=\{',
                ],
                'confidence_weights': [30, 25, 20, 20, 25, 15, 20, 10, 10]
            },
            'Vue': {
                'patterns': [
                    r'import\s+Vue\s+from\s+[\'"]vue[\'"]',
                    r'from\s+[\'"]vue[\'"]',
                    r'new Vue\(',
                    r'Vue\.component',
                    r'v-if|v-for|v-show|v-model',
                    r'@click|@input|@change',
                    r'\{\{\s*.*\s*\}\}',  # Vue模板语法
                    r'\.vue[\'"]',
                    r'<template>|<script>|<style>',
                ],
                'confidence_weights': [30, 25, 25, 20, 20, 15, 15, 15, 20]
            },
            'Angular': {
                'patterns': [
                    r'import.*@angular',
                    r'@Component|@Injectable|@NgModule',
                    r'ng-app|ng-controller',
                    r'\*ngIf|\*ngFor',
                    r'\[ngModel\]|\(click\)',
                    r'angular\.module',
                ],
                'confidence_weights': [30, 25, 20, 20, 15, 15]
            },
            'jQuery': {
                'patterns': [
                    r'\$\(',
                    r'jQuery\(',
                    r'\.jquery',
                    r'\$\.ajax|\$\.get|\$\.post',
                ],
                'confidence_weights': [20, 20, 15, 15]
            }
        }
        
        # 构建工具检测规则
        self.bundler_patterns = {
            'webpack': {
                'patterns': [
                    r'__webpack_require__',
                    r'webpackJsonp',
                    r'webpack:\/\/',
                    r'\/\*\s*webpack\s*\*\/',
                    r'__webpack_exports__',
                    r'module\.exports\s*=\s*__webpack_require__',
                ],
                'confidence_weights': [30, 25, 20, 15, 20, 15]
            },
            'vite': {
                'patterns': [
                    r'import\.meta\.hot',
                    r'/@vite/',
                    r'vite:',
                    r'__vite__',
                ],
                'confidence_weights': [30, 25, 20, 15]
            },
            'rollup': {
                'patterns': [
                    r'rollup',
                    r'createCommonjsModule',
                    r'__rollup__',
                ],
                'confidence_weights': [25, 20, 15]
            },
            'parcel': {
                'patterns': [
                    r'parcelRequire',
                    r'parcel-bundler',
                    r'__parcel__',
                ],
                'confidence_weights': [30, 20, 15]
            },
            'esbuild': {
                'patterns': [
                    r'__esm\(',
                    r'esbuild',
                ],
                'confidence_weights': [25, 20]
            }
        }
        
        # CSS框架检测
        self.css_frameworks = {
            'Bootstrap': [
                r'bootstrap\.css|bootstrap\.min\.css',
                r'class=".*\bbtn\b',
                r'class=".*\bcontainer\b',
                r'class=".*\brow\b',
                r'class=".*\bcol-',
            ],
            'Tailwind CSS': [
                r'tailwindcss',
                r'class=".*\bflex\b',
                r'class=".*\bgrid\b',
                r'class=".*\btext-',
                r'class=".*\bbg-',
                r'class=".*\bp-\d|m-\d',
            ],
            'Ant Design': [
                r'antd',
                r'ant-design',
                r'class=".*\bant-',
            ],
            'Element UI': [
                r'element-ui',
                r'class=".*\bel-',
            ]
        }
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        return 'code' in params or 'file_content' in params
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行代码分析"""
        self._start_execution()
        
        try:
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要code或file_content")
            
            code = params.get('code') or params.get('file_content', '')
            filename = params.get('filename', 'unknown')
            
            logger.info(f"开始分析代码文件: {filename}")
            
            # 执行分析
            analysis_result = self._analyze_code(code, filename)
            
            # 保存结果
            self.add_result(analysis_result)
            
            message = f'成功分析代码文件: {filename}'
            self._complete_execution(True, message)
            
            return {
                'success': True,
                'message': message,
                'data': analysis_result,
                'filename': filename
            }
            
        except Exception as e:
            error_msg = f'代码分析失败: {str(e)}'
            logger.error(error_msg, exc_info=True)
            self._complete_execution(False, error_msg)
            return {
                'success': False,
                'message': error_msg,
                'data': {},
                'filename': params.get('filename', 'unknown')
            }
    
    def _analyze_code(self, code: str, filename: str) -> Dict[str, Any]:
        """分析代码内容"""
        result = {
            'filename': filename,
            'file_type': self._detect_file_type(filename, code),
            'frameworks': self._detect_frameworks(code),
            'bundlers': self._detect_bundlers(code),
            'css_frameworks': self._detect_css_frameworks(code),
            'code_metrics': self._calculate_metrics(code),
            'dependencies': self._extract_dependencies(code),
            'build_info': self._analyze_build_info(code)
        }
        
        return result
    
    def _detect_file_type(self, filename: str, code: str) -> str:
        """检测文件类型"""
        if filename.endswith('.js'):
            return 'javascript'
        elif filename.endswith('.jsx'):
            return 'jsx'
        elif filename.endswith('.ts'):
            return 'typescript'
        elif filename.endswith('.tsx'):
            return 'tsx'
        elif filename.endswith('.vue'):
            return 'vue'
        elif filename.endswith('.css'):
            return 'css'
        elif filename.endswith('.html'):
            return 'html'
        elif filename.endswith('.json'):
            return 'json'
        else:
            # 根据内容推断
            if '<template>' in code and '<script>' in code:
                return 'vue'
            elif 'import' in code and 'export' in code:
                return 'javascript'
            elif '<html>' in code or '<!DOCTYPE' in code:
                return 'html'
            else:
                return 'unknown'
    
    def _detect_frameworks(self, code: str) -> Dict[str, Dict[str, Any]]:
        """检测前端框架"""
        detected = {}
        
        for framework, config in self.framework_patterns.items():
            patterns = config['patterns']
            weights = config['confidence_weights']
            
            confidence = 0
            matched_patterns = []
            
            for i, pattern in enumerate(patterns):
                if re.search(pattern, code, re.IGNORECASE | re.MULTILINE):
                    confidence += weights[i] if i < len(weights) else 10
                    matched_patterns.append(pattern)
            
            if confidence > 0:
                detected[framework] = {
                    'confidence': min(confidence, 100),
                    'matched_patterns': matched_patterns,
                    'evidence_count': len(matched_patterns)
                }
        
        return detected
    
    def _detect_bundlers(self, code: str) -> Dict[str, Dict[str, Any]]:
        """检测构建工具"""
        detected = {}
        
        for bundler, config in self.bundler_patterns.items():
            patterns = config['patterns']
            weights = config['confidence_weights']
            
            confidence = 0
            matched_patterns = []
            
            for i, pattern in enumerate(patterns):
                if re.search(pattern, code, re.IGNORECASE | re.MULTILINE):
                    confidence += weights[i] if i < len(weights) else 10
                    matched_patterns.append(pattern)
            
            if confidence > 0:
                detected[bundler] = {
                    'confidence': min(confidence, 100),
                    'matched_patterns': matched_patterns,
                    'evidence_count': len(matched_patterns)
                }
        
        return detected
    
    def _detect_css_frameworks(self, code: str) -> Dict[str, int]:
        """检测CSS框架"""
        detected = {}
        
        for framework, patterns in self.css_frameworks.items():
            confidence = 0
            for pattern in patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    confidence += 20
            
            if confidence > 0:
                detected[framework] = min(confidence, 100)
        
        return detected
    
    def _calculate_metrics(self, code: str) -> Dict[str, Any]:
        """计算代码指标"""
        lines = code.split('\n')
        
        return {
            'total_lines': len(lines),
            'code_lines': len([line for line in lines if line.strip() and not line.strip().startswith('//')]),
            'comment_lines': len([line for line in lines if line.strip().startswith('//')]),
            'blank_lines': len([line for line in lines if not line.strip()]),
            'character_count': len(code),
            'function_count': len(re.findall(r'function\s+\w+|const\s+\w+\s*=\s*\(.*\)\s*=>', code)),
            'import_count': len(re.findall(r'import\s+.*from', code)),
            'export_count': len(re.findall(r'export\s+', code))
        }
    
    def _extract_dependencies(self, code: str) -> List[str]:
        """提取依赖项"""
        dependencies = set()
        
        # 提取import语句中的依赖
        import_matches = re.findall(r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]', code)
        dependencies.update(import_matches)
        
        # 提取require语句中的依赖
        require_matches = re.findall(r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)', code)
        dependencies.update(require_matches)
        
        return list(dependencies)
    
    def _analyze_build_info(self, code: str) -> Dict[str, Any]:
        """分析构建信息"""
        build_info = {
            'is_minified': self._is_minified(code),
            'has_source_map': 'sourceMappingURL' in code,
            'has_webpack_chunks': '__webpack_require__' in code,
            'has_hot_reload': 'module.hot' in code or 'import.meta.hot' in code,
            'build_timestamp': self._extract_build_timestamp(code)
        }
        
        return build_info
    
    def _is_minified(self, code: str) -> bool:
        """判断代码是否被压缩"""
        lines = code.split('\n')
        if len(lines) < 10:
            return False
        
        # 计算平均行长度
        avg_line_length = sum(len(line) for line in lines) / len(lines)
        
        # 如果平均行长度很长且换行很少，可能是压缩的
        return avg_line_length > 200 and len(lines) < 50
    
    def _extract_build_timestamp(self, code: str) -> str:
        """提取构建时间戳"""
        # 查找常见的时间戳格式
        timestamp_patterns = [
            r'build.*?(\d{4}-\d{2}-\d{2})',
            r'timestamp.*?(\d{13})',
            r'version.*?(\d+\.\d+\.\d+)',
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, code, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return 'unknown'
