#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤1: 域名源码获取器
批量输入域名并获取网页源码
"""

import asyncio
import aiohttp
import requests
from typing import Dict, List, Any
from urllib.parse import urlparse, urljoin
import re
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

from .base_step import BaseStep

class DomainFetcher(BaseStep):
    """域名源码获取器"""
    
    def __init__(self, step_id: str, name: str, description: str):
        super().__init__(step_id, name, description)
        self.timeout = 30
        self.max_concurrent = 5
    
    def validate_params(self, params: Dict[str, Any]) -> bool:
        """验证参数"""
        if 'domains' not in params:
            return False
        
        domains = params['domains']
        if not isinstance(domains, list) or len(domains) == 0:
            return False
        
        return True
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行域名源码获取"""
        self._start_execution()
        
        try:
            # 验证参数
            if not self.validate_params(params):
                raise ValueError("参数验证失败: 需要domains列表")
            
            domains = params['domains']
            method = params.get('method', 'requests')  # requests, selenium, playwright
            
            # 清理和标准化域名
            cleaned_domains = self._clean_domains(domains)
            
            if method == 'selenium':
                results = self._fetch_with_selenium(cleaned_domains)
            elif method == 'playwright':
                results = asyncio.run(self._fetch_with_playwright(cleaned_domains))
            else:  # requests
                results = asyncio.run(self._fetch_with_requests(cleaned_domains))
            
            # 存储结果
            for result in results:
                self.add_result(result)
            
            success_count = len([r for r in results if r['success']])
            
            self._complete_execution(True, f"成功获取 {success_count}/{len(domains)} 个域名的源码")
            
            return {
                'success': True,
                'message': f"成功获取 {success_count}/{len(domains)} 个域名的源码",
                'data': results,
                'total': len(domains),
                'success_count': success_count
            }
            
        except Exception as e:
            self._complete_execution(False, str(e))
            return {
                'success': False,
                'message': f"执行失败: {str(e)}",
                'data': [],
                'total': 0,
                'success_count': 0
            }
    
    def _clean_domains(self, domains: List[str]) -> List[str]:
        """清理和标准化域名"""
        cleaned = []
        for domain in domains:
            domain = domain.strip()
            if not domain:
                continue
            
            # 添加协议
            if not domain.startswith(('http://', 'https://')):
                domain = 'https://' + domain
            
            cleaned.append(domain)
        
        return list(set(cleaned))  # 去重
    
    async def _fetch_with_requests(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用requests异步获取"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def fetch_single(session, domain):
            async with semaphore:
                return await self._fetch_single_requests(session, domain)
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            tasks = [fetch_single(session, domain) for domain in domains]
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'domain': domains[i],
                    'success': False,
                    'error': str(result),
                    'source_code': '',
                    'response_time': 0,
                    'status_code': 0
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _fetch_single_requests(self, session: aiohttp.ClientSession, domain: str) -> Dict[str, Any]:
        """单个域名请求"""
        start_time = time.time()
        
        try:
            async with session.get(domain, allow_redirects=True) as response:
                source_code = await response.text()
                response_time = time.time() - start_time
                
                return {
                    'domain': domain,
                    'success': True,
                    'source_code': source_code,
                    'response_time': response_time,
                    'status_code': response.status,
                    'final_url': str(response.url),
                    'content_length': len(source_code),
                    'error': ''
                }
        
        except Exception as e:
            return {
                'domain': domain,
                'success': False,
                'source_code': '',
                'response_time': time.time() - start_time,
                'status_code': 0,
                'final_url': domain,
                'content_length': 0,
                'error': str(e)
            }
    
    def _fetch_with_selenium(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用selenium获取（支持JavaScript渲染）"""
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        results = []
        driver = None
        
        try:
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(self.timeout)
            
            for domain in domains:
                start_time = time.time()
                
                try:
                    driver.get(domain)
                    
                    # 等待页面加载完成
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                    
                    source_code = driver.page_source
                    response_time = time.time() - start_time
                    final_url = driver.current_url
                    
                    results.append({
                        'domain': domain,
                        'success': True,
                        'source_code': source_code,
                        'response_time': response_time,
                        'status_code': 200,  # Selenium不提供状态码
                        'final_url': final_url,
                        'content_length': len(source_code),
                        'error': ''
                    })
                    
                except Exception as e:
                    results.append({
                        'domain': domain,
                        'success': False,
                        'source_code': '',
                        'response_time': time.time() - start_time,
                        'status_code': 0,
                        'final_url': domain,
                        'content_length': 0,
                        'error': str(e)
                    })
        
        finally:
            if driver:
                driver.quit()
        
        return results
    
    async def _fetch_with_playwright(self, domains: List[str]) -> List[Dict[str, Any]]:
        """使用playwright获取（支持JavaScript渲染，更现代）"""
        try:
            from playwright.async_api import async_playwright
        except ImportError:
            raise ImportError("需要安装playwright: pip install playwright")
        
        results = []
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            
            try:
                for domain in domains:
                    start_time = time.time()
                    page = await context.new_page()
                    
                    try:
                        response = await page.goto(domain, timeout=self.timeout * 1000)
                        await page.wait_for_load_state('networkidle', timeout=10000)
                        
                        source_code = await page.content()
                        response_time = time.time() - start_time
                        final_url = page.url
                        status_code = response.status if response else 200
                        
                        results.append({
                            'domain': domain,
                            'success': True,
                            'source_code': source_code,
                            'response_time': response_time,
                            'status_code': status_code,
                            'final_url': final_url,
                            'content_length': len(source_code),
                            'error': ''
                        })
                        
                    except Exception as e:
                        results.append({
                            'domain': domain,
                            'success': False,
                            'source_code': '',
                            'response_time': time.time() - start_time,
                            'status_code': 0,
                            'final_url': domain,
                            'content_length': 0,
                            'error': str(e)
                        })
                    
                    finally:
                        await page.close()
            
            finally:
                await browser.close()
        
        return results
