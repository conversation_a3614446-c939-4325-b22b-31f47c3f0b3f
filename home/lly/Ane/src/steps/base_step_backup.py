#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤基类 - 所有工作流步骤的基础类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any
from enum import Enum
from datetime import datetime
from loguru import logger

class StepStatus(Enum):
    """步骤状态枚举"""
    READY = "ready"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"

class BaseStep(ABC):
    """步骤基类"""
    
    def __init__(self, step_id: str, name: str, description: str):
        self.step_id = step_id
        self.name = name
        self.description = description
        self.status = StepStatus.READY
        self.results: List[Dict[str, Any]] = []
        self.error_message: str = ""
        self.start_time: datetime = None
        self.end_time: datetime = None
    
    @abstractmethod
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行步骤的主要逻辑
        
        Args:
            params: 执行参数
            
        Returns:
            Dict包含执行结果
        """
        pass
    
    @abstractmethod
    def validate_params(self, params: Dict[str, Any]) -> bool:
        """
        验证输入参数
        
        Args:
            params: 待验证的参数
            
        Returns:
            bool: 参数是否有效
        """
        pass
    
    def can_run(self) -> bool:
        """检查步骤是否可以运行"""
        return self.status in [StepStatus.READY, StepStatus.COMPLETED]
    
    def reset(self):
        """重置步骤状态"""
        self.status = StepStatus.READY
        self.results.clear()
        self.error_message = ""
        self.start_time = None
        self.end_time = None
    
    def clear_results(self):
        """清空结果"""
        self.results.clear()
    
    def add_result(self, result: Dict[str, Any]):
        """添加结果"""
        result['timestamp'] = datetime.now().isoformat()
        self.results.append(result)
    
    def get_execution_time(self) -> float:
        """获取执行时间（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0
    
    def _start_execution(self):
        """开始执行前的准备"""
        self.status = StepStatus.RUNNING
        self.start_time = datetime.now()
        self.error_message = ""
        logger.info(f"步骤 {self.name} 开始执行")
    
    def _complete_execution(self, success: bool = True, message: str = ""):
        """完成执行后的清理"""
        self.end_time = datetime.now()
        if success:
            self.status = StepStatus.COMPLETED
            logger.info(f"步骤 {self.name} 执行完成，耗时 {self.get_execution_time():.2f}秒")
        else:
            self.status = StepStatus.ERROR
            self.error_message = message
            logger.error(f"步骤 {self.name} 执行失败: {message}")
    
    def get_step_info(self) -> Dict[str, Any]:
        """获取步骤信息"""
        return {
            'step_id': self.step_id,
            'name': self.name,
            'description': self.description,
            'status': self.status.value,
            'results_count': len(self.results),
            'execution_time': self.get_execution_time(),
            'error_message': self.error_message,
            'can_run': self.can_run()
        }
