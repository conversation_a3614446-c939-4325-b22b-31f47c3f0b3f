#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD5转UTF-8转换器
将字符串的MD5值转换为小端字节序，然后尝试UTF-8解码，过滤控制字符
"""

import hashlib
import struct


def md5_to_little_endian_utf8(input_string: str, filter_control_chars: bool = True, require_input_newline_ending: bool = True) -> dict:
    """
    将字符串的MD5值转换为小端字节序，然后尝试UTF-8解码

    Args:
        input_string (str): 输入字符串
        filter_control_chars (bool): 是否过滤控制字符，默认True
        require_input_newline_ending (bool): 是否要求输入字符串以换行符结尾，默认True

    Returns:
        dict: 包含转换结果的字典
            - original: 原始字符串
            - md5_hex: MD5十六进制值
            - md5_bytes: MD5字节数组
            - little_endian_bytes: 小端字节数组
            - utf8_result: UTF-8解码结果
            - filtered_result: 过滤控制字符后的结果
            - has_control_chars: 是否包含控制字符
            - decode_errors: 解码错误信息
            - input_ends_with_newline: 输入字符串是否以换行符结尾
            - meets_requirements: 是否满足所有要求
    """
    input_string = "f1a78b78-dc13-4984-8619-0a0be996dd38"+'\n'
    # 1. 计算MD5值
    md5_hash = hashlib.md5(input_string.encode('utf-8')).hexdigest()
    
    # 2. 将MD5十六进制字符串转换为字节数组
    md5_bytes = bytes.fromhex(md5_hash)
    
    # 3. 转换为小端字节序（反转字节顺序）
    little_endian_bytes = md5_bytes[::-1]
    little_endian_bytes = md5_bytes
    
    # 4. 尝试UTF-8解码
    utf8_result = ""
    decode_errors = []

    try:
        # 尝试完整解码
        utf8_result = little_endian_bytes.decode('utf-8')
    except UnicodeDecodeError:
        # 如果完整解码失败，使用错误处理策略
        try:
            # 使用'replace'策略，将无法解码的字节替换为替换字符
            utf8_result = little_endian_bytes.decode('utf-8', errors='replace')
        except Exception:
            # 如果还是失败，逐字节处理
            utf8_result = ""
            for i, byte_val in enumerate(little_endian_bytes):
                try:
                    # 尝试将单个字节作为Latin-1解码（总是成功）
                    char = chr(byte_val)
                    utf8_result += char
                except Exception as e:
                    decode_errors.append(f"字节 {i}: 0x{byte_val:02x} - {str(e)}")
                    utf8_result += f"\\x{byte_val:02x}"
    
    # 5. 检查和过滤控制字符（但保留换行符）
    has_control_chars = False
    filtered_result = ""

    if filter_control_chars:
        for char in utf8_result:
            if is_control_character(char) and char != '\n':  # 保留换行符
                has_control_chars = True
                return {

                        'meets_requirements': False,
        
                    }
            else:
                filtered_result += char
    else:
        filtered_result = utf8_result
        # 检查是否有控制字符（换行符不算）
        has_control_chars = any(is_control_character(char) and char != '\n' for char in utf8_result)

    # 6. 检查输入字符串是否以换行符结尾
    input_ends_with_newline = input_string.endswith('\n')

    # 7. 检查是否满足所有要求
    meets_requirements = True
    if filter_control_chars and has_control_chars:
        meets_requirements = False
    if require_input_newline_ending and not input_ends_with_newline:
        meets_requirements = False
    if decode_errors:
        meets_requirements = False
    
    return {
        'original': input_string,
        'md5_hex': md5_hash,
        'md5_bytes': list(md5_bytes),
        'little_endian_bytes': list(little_endian_bytes),
        'little_endian_hex': little_endian_bytes.hex(),
        'utf8_result': utf8_result,
        'filtered_result': filtered_result,
        'has_control_chars': has_control_chars,
        'decode_errors': decode_errors,
        'input_ends_with_newline': input_ends_with_newline,
        'meets_requirements': meets_requirements,
        'result_length': len(filtered_result),
        'original_bytes_length': len(little_endian_bytes)
    }


def is_control_character(char: str) -> bool:
    """
    判断字符是否为控制字符
    
    Args:
        char (str): 要检查的字符
    
    Returns:
        bool: 是否为控制字符
    """
    if not char:
        return False
    
    # 获取字符的Unicode码点
    code_point = ord(char)
    
    # ASCII控制字符 (0x00-0x1F, 0x7F)
    if 0x00 <= code_point <= 0x1F or code_point == 0x7F:
        return True
    
    # Unicode控制字符类别
    import unicodedata
    category = unicodedata.category(char)
    
    # Cc: Control characters
    # Cf: Format characters (某些格式字符也被认为是控制字符)
    if category in ['Cc', 'Cf']:
        return True
    
    return False


def find_string_without_control_chars(target_pattern: str = None, max_attempts: int = 10000, require_input_newline_ending: bool = True) -> dict:
    """
    寻找一个字符串，使其MD5转换后的UTF-8结果不包含控制字符，且输入字符串以换行符结尾

    Args:
        target_pattern (str): 目标模式（可选）
        max_attempts (int): 最大尝试次数
        require_input_newline_ending (bool): 是否要求输入字符串以换行符结尾

    Returns:
        dict: 找到的结果或None
    """
    import random
    import string
    
    for attempt in range(max_attempts):
        # 生成随机字符串
        if target_pattern:
            test_string = target_pattern + str(attempt)
        else:
            length = random.randint(1, 20)
            test_string = ''.join(random.choices(
                string.ascii_letters + string.digits + string.punctuation,
                k=length
            ))

        # 如果要求输入字符串以换行符结尾，则添加换行符
        if require_input_newline_ending:
            test_string += '\n'

        result = md5_to_little_endian_utf8(test_string, require_input_newline_ending=require_input_newline_ending)

        # 如果满足所有要求
        if result['meets_requirements']:
            result['attempts'] = attempt + 1
            return result
    
    return None


def demonstrate_conversion():
    """演示转换功能"""
    print("=== MD5转UTF-8转换器演示 ===\n")
    
    # 测试用例
    test_cases = ["a", "hello", "test123", "中文测试", "!@#$%^&*()"]
    
    for test_string in test_cases:
        print(f"输入字符串: '{test_string}'")
        result = md5_to_little_endian_utf8(test_string)
        
        print(f"MD5值: {result['md5_hex']}")
        print(f"小端字节序: {result['little_endian_hex']}")
        print(f"UTF-8解码结果: '{repr(result['utf8_result'])}'")
        print(f"过滤后结果: '{repr(result['filtered_result'])}'")
        print(f"包含控制字符: {result['has_control_chars']}")
        print(f"输入以换行符结尾: {result['input_ends_with_newline']}")
        print(f"满足所有要求: {result['meets_requirements']}")
        if result['decode_errors']:
            print(f"解码错误: {result['decode_errors']}")
        print("-" * 50)
    
    # 寻找符合条件的字符串
    print("\n=== 寻找符合所有条件的字符串 ===")
    print("条件：1) 输入字符串以换行符结尾 2) UTF-8解码结果不包含控制字符（除换行符外）3) 可以UTF-8解码")

    clean_result = find_string_without_control_chars("test", 10000, require_input_newline_ending=True)
    if clean_result:
        print(f"找到符合条件的字符串: '{clean_result['original']}'")
        print(f"尝试次数: {clean_result['attempts']}")
        print(f"MD5值: {clean_result['md5_hex']}")
        print(f"小端字节序: {clean_result['little_endian_hex']}")
        print(f"UTF-8结果: {repr(clean_result['filtered_result'])}")
        print(f"输入以换行符结尾: {clean_result['input_ends_with_newline']}")
        print(f"满足所有要求: {clean_result['meets_requirements']}")
    else:
        print("未找到符合条件的字符串，尝试增加搜索次数或调整条件")


def find_perfect_string(prefix: str = "test", max_attempts: int = 50000) -> dict:
    """
    寻找一个完美的字符串，满足所有条件

    Args:
        prefix (str): 字符串前缀
        max_attempts (int): 最大尝试次数

    Returns:
        dict: 找到的完美字符串结果
    """
    print(f"正在寻找以'{prefix}'开头的完美字符串...")
    print("要求：")
    print("1. 输入字符串必须以换行符结尾")
    print("2. MD5值转小端字节序后可以UTF-8解码")
    print("3. 解码结果不包含控制字符（换行符除外）")
    print()

    result = find_string_without_control_chars(prefix, max_attempts, require_input_newline_ending=True)

    if result:
        print("🎉 找到完美字符串！")
        print(f"原始字符串: '{result['original']}'")
        print(f"尝试次数: {result['attempts']}")
        print(f"MD5值: {result['md5_hex']}")
        print(f"MD5字节数组: {result['md5_bytes']}")
        print(f"小端字节数组: {result['little_endian_bytes']}")
        print(f"小端十六进制: {result['little_endian_hex']}")
        print(f"UTF-8解码结果: {repr(result['filtered_result'])}")
        print(f"结果长度: {result['result_length']} 字符")
        print(f"输入以换行符结尾: {result['input_ends_with_newline']}")
        print(f"包含控制字符: {result['has_control_chars']}")
        print(f"满足所有要求: {result['meets_requirements']}")

        # 验证输入字符串的换行符
        if result['original'].endswith('\n'):
            print("✅ 确认：输入字符串以换行符结尾")
        else:
            print("❌ 错误：输入字符串未以换行符结尾")

        return result
    else:
        print(f"❌ 在{max_attempts}次尝试中未找到符合条件的字符串")
        return None


if __name__ == "__main__":
    # demonstrate_conversion()

    # 寻找完美字符串
    perfect_result = find_perfect_string("hello", 20000)

    if perfect_result:
        print("\n" + "="*60)
        print("完美字符串详细信息:")
        print("="*60)

        # 显示字节到字符的映射
        bytes_array = perfect_result['little_endian_bytes']
        utf8_result = perfect_result['filtered_result']

        print(f"字节数组: {bytes_array}")
        print(f"十六进制: {perfect_result['little_endian_hex']}")
        print(f"UTF-8字符: {repr(utf8_result)}")

        # 验证每个字符
        print("\n字符分析:")
        for i, char in enumerate(utf8_result):
            if char == '\n':
                print(f"位置 {i}: '\\n' (换行符) - Unicode: U+{ord(char):04X}")
            elif ord(char) < 32 or ord(char) == 127:
                print(f"位置 {i}: {repr(char)} (控制字符) - Unicode: U+{ord(char):04X}")
            else:
                print(f"位置 {i}: '{char}' - Unicode: U+{ord(char):04X}")
    else:
        print("\n尝试其他前缀...")
        for prefix in ["test", "data", "key", "str", "val"]:
            result = find_perfect_string(prefix, 10000)
            if result:
                break
