#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Browser-use包装器 - 提供统一的浏览器自动化接口
"""

import time
import logging
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)

class BrowserUseWrapper:
    """Browser-use包装器，提供智能浏览器自动化功能"""
    
    def __init__(self, headless: bool = True, timeout: int = 30):
        self.headless = headless
        self.timeout = timeout
        self.driver = None
        self._init_driver()
    
    def _init_driver(self):
        """初始化浏览器驱动"""
        options = Options()
        if self.headless:
            options.add_argument("--headless")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--disable-extensions")
        options.add_argument("--proxy-server='direct://'")
        options.add_argument("--proxy-bypass-list=*")
        options.add_argument("--disable-software-rasterizer")
        options.page_load_strategy = 'normal'
        
        # 启用性能日志
        options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=options)
            self.driver.set_page_load_timeout(self.timeout)
            
            # 启用网络跟踪
            self.driver.execute_cdp_cmd('Network.enable', {})
            
            logger.info("Browser-use包装器初始化成功")
        except Exception as e:
            logger.error(f"Browser-use包装器初始化失败: {str(e)}")
            raise
    
    def get_page_info(self, url: str) -> Dict[str, Any]:
        """获取页面信息"""
        if not self.driver:
            return {
                'success': False,
                'error': '浏览器驱动未初始化'
            }
        
        try:
            logger.info(f"正在访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载完成
            WebDriverWait(self.driver, self.timeout).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            
            # 额外等待动态内容
            time.sleep(1)
            
            # 获取页面信息
            html_content = self.driver.page_source
            final_url = self.driver.current_url
            
            # 获取资源信息
            resources = self._capture_resources()
            
            return {
                'success': True,
                'html': html_content,
                'final_url': final_url,
                'status_code': 200,  # 浏览器不提供状态码
                'resources': resources
            }
            
        except TimeoutException:
            error_msg = f"页面加载超时（{self.timeout}秒）"
            logger.error(f"获取 {url} 失败: {error_msg}")
            
            # 尝试获取部分内容
            try:
                html_content = self.driver.page_source
                final_url = self.driver.current_url
                return {
                    'success': True,
                    'html': html_content,
                    'final_url': final_url,
                    'status_code': 200,
                    'resources': {},
                    'warning': '页面加载超时，获取到部分内容'
                }
            except:
                return {
                    'success': False,
                    'error': error_msg
                }
        
        except Exception as e:
            error_msg = f"获取页面失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg
            }
    
    def _capture_resources(self) -> Dict[str, List[Dict[str, str]]]:
        """捕获页面资源"""
        resources = {
            'scripts': [],
            'styles': [],
            'images': [],
            'other': []
        }
        
        try:
            # 获取性能日志
            logs = self.driver.get_log('performance')
            seen_urls = set()
            
            for log in logs:
                message = log.get('message', {})
                if isinstance(message, str):
                    import json
                    try:
                        message = json.loads(message)
                    except:
                        continue
                
                if message.get('message', {}).get('method') == 'Network.responseReceived':
                    response = message['message']['params']['response']
                    url = response.get('url', '')
                    mime_type = response.get('mimeType', '')
                    
                    if url in seen_urls or not url.startswith('http'):
                        continue
                    
                    seen_urls.add(url)
                    
                    resource_info = {
                        'url': url,
                        'type': mime_type,
                        'filename': url.split('/')[-1] if '/' in url else url
                    }
                    
                    # 分类资源
                    if 'javascript' in mime_type or url.endswith('.js'):
                        resources['scripts'].append(resource_info)
                    elif 'css' in mime_type or url.endswith('.css'):
                        resources['styles'].append(resource_info)
                    elif 'image' in mime_type or any(url.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']):
                        resources['images'].append(resource_info)
                    else:
                        resources['other'].append(resource_info)
        
        except Exception as e:
            logger.error(f"捕获资源失败: {str(e)}")
        
        return resources
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Browser-use包装器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器失败: {str(e)}")
            finally:
                self.driver = None

# 为了兼容性，提供Browser类别名
Browser = BrowserUseWrapper
