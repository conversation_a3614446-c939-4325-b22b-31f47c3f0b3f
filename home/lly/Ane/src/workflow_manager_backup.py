#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作流管理器 - 负责管理和编排所有步骤
"""

from typing import Dict, List, Any, Optional
from enum import Enum
import json
from datetime import datetime
from loguru import logger

from .steps.base_step import BaseStep
from .steps.domain_fetcher import DomainFetcher
from .steps.framework_analyzer import FrameworkAnalyzer
from .database.db_manager import DatabaseManager

class WorkflowStatus(Enum):
    """工作流状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"

class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self):
        self.steps: Dict[str, BaseStep] = {}
        self.workflow_history: List[Dict] = []
        self.current_step: Optional[str] = None
        self.status = WorkflowStatus.IDLE
        self.db_manager = DatabaseManager()
        self._initialize_steps()
    
    def _initialize_steps(self):
        """初始化所有步骤"""
        # 步骤1: 域名获取器
        self.steps['domain_fetcher'] = DomainFetcher(
            step_id='domain_fetcher',
            name='域名源码获取',
            description='批量输入域名并获取网页源码'
        )
        
        # 步骤2: 框架分析器
        self.steps['framework_analyzer'] = FrameworkAnalyzer(
            step_id='framework_analyzer', 
            name='前端框架分析',
            description='分析源码中的前端框架和map文件'
        )
        
        # 预留步骤3-N的接口
        # TODO: 在这里添加更多步骤
        
    def get_available_steps(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用步骤"""
        return {
            step_id: {
                'name': step.name,
                'description': step.description,
                'status': step.status.value,
                'can_run': step.can_run(),
                'results_count': len(step.results)
            }
            for step_id, step in self.steps.items()
        }
    
    def run_step(self, step_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行指定步骤"""
        if step_id not in self.steps:
            raise ValueError(f"步骤 {step_id} 不存在")
        
        step = self.steps[step_id]
        
        try:
            self.current_step = step_id
            self.status = WorkflowStatus.RUNNING
            
            logger.info(f"开始执行步骤: {step.name}")
            
            # 执行步骤
            result = step.execute(params or {})
            
            # 记录执行历史
            self._record_execution(step_id, result, params)
            
            self.status = WorkflowStatus.COMPLETED
            logger.info(f"步骤 {step.name} 执行完成")
            
            return result
            
        except Exception as e:
            self.status = WorkflowStatus.ERROR
            logger.error(f"步骤 {step.name} 执行失败: {str(e)}")
            raise
        finally:
            self.current_step = None
    
    def get_step_results(self, step_id: str) -> List[Dict[str, Any]]:
        """获取步骤结果"""
        if step_id not in self.steps:
            return []
        return self.steps[step_id].results
    
    def clear_step_results(self, step_id: str):
        """清空步骤结果"""
        if step_id in self.steps:
            self.steps[step_id].clear_results()
    
    def reset_workflow(self):
        """重置整个工作流"""
        for step in self.steps.values():
            step.reset()
        self.workflow_history.clear()
        self.current_step = None
        self.status = WorkflowStatus.IDLE
        logger.info("工作流已重置")
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            'status': self.status.value,
            'current_step': self.current_step,
            'total_steps': len(self.steps),
            'completed_steps': len([s for s in self.steps.values() if s.results]),
            'last_execution': self.workflow_history[-1] if self.workflow_history else None
        }
    
    def _record_execution(self, step_id: str, result: Dict[str, Any], params: Dict[str, Any]):
        """记录执行历史"""
        record = {
            'step_id': step_id,
            'step_name': self.steps[step_id].name,
            'timestamp': datetime.now().isoformat(),
            'params': params,
            'result_summary': {
                'success': result.get('success', False),
                'total_items': len(result.get('data', [])),
                'message': result.get('message', '')
            }
        }
        self.workflow_history.append(record)
    
    def export_results(self, format_type: str = 'json') -> str:
        """导出所有结果"""
        all_results = {}
        for step_id, step in self.steps.items():
            if step.results:
                all_results[step_id] = {
                    'step_name': step.name,
                    'results': step.results
                }
        
        if format_type == 'json':
            return json.dumps(all_results, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
