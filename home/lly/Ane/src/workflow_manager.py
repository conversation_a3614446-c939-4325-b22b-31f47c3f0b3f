#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级工作流管理器 - 无外部依赖版本
"""

from typing import Dict, List, Any, Optional
from enum import Enum
import json
from datetime import datetime

from .steps.base_step import BaseStep
from .steps.domain_fetcher import DomainFetcher  
from .steps.framework_analyzer import FrameworkAnalyzer
from .database.db_manager import DatabaseManager

class WorkflowStatus(Enum):
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"

class WorkflowManager:
    """轻量级工作流管理器"""
    
    def __init__(self):
        self.steps: Dict[str, BaseStep] = {}
        self.workflow_history: List[Dict] = []
        self.current_step: Optional[str] = None
        self.status = WorkflowStatus.IDLE
        self.db_manager = DatabaseManager()
        self._initialize_steps()
    
    def _initialize_steps(self):
        """初始化可用步骤"""
        self.steps['domain_fetcher'] = DomainFetcher(
            step_id='domain_fetcher',
            name='域名源码获取',
            description='批量输入域名并获取网页源码'
        )
        
        self.steps['framework_analyzer'] = FrameworkAnalyzer(
            step_id='framework_analyzer', 
            name='前端框架分析',
            description='分析源码中的前端框架和map文件'
        )
    
    def get_available_steps(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用步骤"""
        return {
            step_id: {
                'name': step.name,
                'description': step.description,
                'status': step.status.value,
                'can_run': step.can_run(),
                'results_count': len(step.results)
            }
            for step_id, step in self.steps.items()
        }
    
    def run_step(self, step_id: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行指定步骤"""
        if step_id not in self.steps:
            raise ValueError(f"步骤 {step_id} 不存在")
        
        step = self.steps[step_id]
        
        try:
            self.current_step = step_id
            self.status = WorkflowStatus.RUNNING
            
            print(f"开始执行步骤: {step.name}")
            result = step.execute(params or {})
            
            self._record_execution(step_id, result, params)
            self.status = WorkflowStatus.COMPLETED
            print(f"步骤 {step.name} 执行完成")
            
            return result
            
        except Exception as e:
            self.status = WorkflowStatus.ERROR
            print(f"步骤 {step.name} 执行失败: {str(e)}")
            raise
        finally:
            self.current_step = None
    
    def get_step_results(self, step_id: str) -> List[Dict[str, Any]]:
        """获取步骤结果"""
        if step_id not in self.steps:
            return []
        return self.steps[step_id].results
    
    def clear_step_results(self, step_id: str):
        """清空步骤结果"""
        if step_id in self.steps:
            self.steps[step_id].clear_results()
    
    def reset_workflow(self):
        """重置整个工作流"""
        for step in self.steps.values():
            step.reset()
        self.workflow_history.clear()
        self.current_step = None
        self.status = WorkflowStatus.IDLE
        print("工作流已重置")
        
    def ensure_idle_state(self):
        """确保工作流处于空闲状态，如果不是则重置"""
        if self.status != WorkflowStatus.IDLE:
            print(f"警告：工作流状态为 {self.status.value}，正在重置为 IDLE")
            self.reset_workflow()
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return {
            'status': self.status.value,
            'current_step': self.current_step,
            'total_steps': len(self.steps),
            'completed_steps': len([s for s in self.steps.values() if s.results]),
            'last_execution': self.workflow_history[-1] if self.workflow_history else None
        }
    
    def _record_execution(self, step_id: str, result: Dict[str, Any], params: Dict[str, Any]):
        """记录执行历史"""
        record = {
            'step_id': step_id,
            'step_name': self.steps[step_id].name,
            'timestamp': datetime.now().isoformat(),
            'params': params,
            'result_summary': {
                'success': result.get('success', False),
                'total_items': len(result.get('data', [])),
                'message': result.get('message', '')
            }
        }
        self.workflow_history.append(record)
    
    def run_entire_workflow(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行整个工作流"""
        # 确保工作流处于空闲状态
        self.ensure_idle_state()
        
        if self.status != WorkflowStatus.IDLE:
            return {
                'success': False,
                'message': f'工作流状态异常 ({self.status.value})，无法启动新工作流'
            }
        
        try:
            self.status = WorkflowStatus.RUNNING
            
            # 执行域名获取步骤
            domain_result = self.run_step('domain_fetcher', params.get('domain_fetcher', {}))
            if not domain_result['success']:
                return {
                    'success': False,
                    'message': f"域名获取步骤失败: {domain_result['message']}"
                }
            
            # 准备框架分析参数
            framework_params = {
                'source_data': [r for r in domain_result['data'] if r.get('success')]
            }
            
            # 执行框架分析步骤
            framework_result = self.run_step('framework_analyzer', framework_params)
            if not framework_result['success']:
                return {
                    'success': False,
                    'message': f"框架分析步骤失败: {framework_result['message']}"
                }
            
            # 工作流执行完成后重置状态
            self.status = WorkflowStatus.IDLE
            return {
                'success': True,
                'message': '工作流执行完成',
                'results': {
                    'domain_fetcher': domain_result,
                    'framework_analyzer': framework_result
                }
            }
        except Exception as e:
            self.status = WorkflowStatus.ERROR
            error_result = {
                'success': False,
                'message': f"工作流执行异常: {str(e)}"
            }
            # 发生异常后也重置状态
            self.status = WorkflowStatus.IDLE
            return error_result
    
    def export_results(self, format_type: str = 'json') -> str:
        """导出所有结果"""
        all_results = {}
        for step_id, step in self.steps.items():
            if step.results:
                all_results[step_id] = {
                    'step_name': step.name,
                    'results': step.results
                }
        
        if format_type == 'json':
            return json.dumps(all_results, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format_type}")
