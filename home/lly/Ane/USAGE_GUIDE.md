# 🔍 Ane Web Analyzer 使用指南

## 1. 基础使用流程

### 启动应用
```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
streamlit run main.py
```

### 访问界面
打开浏览器访问: http://localhost:8501

## 2. 功能使用

### 步骤1: 域名源码获取
1. 点击侧边栏「步骤执行」
2. 选择「域名源码获取」
3. 在文本框中输入域名（每行一个）:
   ```
   https://www.baidu.com
   github.com
   www.google.com
   ```
4. 选择获取方式（当前支持requests）
5. 点击「开始获取」

### 步骤2: 前端框架分析
1. 确保已完成步骤1
2. 选择「前端框架分析」
3. 点击「开始分析」
4. 查看检测结果

### 结果查看
- 「结果查看」页面：查看详细数据
- 「数据统计」页面：查看统计图表
- 「工作流管理」页面：管理步骤状态

## 3. 数据管理
- 所有结果自动保存到SQLite数据库
- 支持CSV格式导出
- 可在设置页面清空或导出数据

## 4. 扩展开发示例

### 添加新步骤
```python
# 新建文件: src/steps/my_custom_step.py
from .base_step import BaseStep
from typing import Dict, List, Any

class MyCustomStep(BaseStep):
    def validate_params(self, params: Dict[str, Any]) -> bool:
        # 验证输入参数
        return True
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        self._start_execution()
        try:
            # 实现你的分析逻辑
            results = []
            # ... 处理逻辑 ...
            
            self._complete_execution(True, '执行完成')
            return {
                'success': True,
                'message': '执行成功',
                'data': results
            }
        except Exception as e:
            self._complete_execution(False, str(e))
            return {'success': False, 'message': str(e)}
```

### 注册新步骤
在 `workflow_manager.py` 中添加:
```python
from .steps.my_custom_step import MyCustomStep

# 在 _initialize_steps 方法中添加
self.steps['my_custom'] = MyCustomStep(
    step_id='my_custom',
    name='我的自定义步骤',
    description='这是一个自定义的分析步骤'
)
```

