#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ane - Web Analyzer Project
主程序入口文件
"""

import streamlit as st
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.workflow_manager import WorkflowManager
from ui.main_ui import MainUI

def main():
    """主程序入口"""
    st.set_page_config(
        page_title="Ane - Web Analyzer",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 初始化工作流管理器
    if 'workflow_manager' not in st.session_state:
        st.session_state.workflow_manager = WorkflowManager()
    
    # 启动UI
    ui = MainUI(st.session_state.workflow_manager)
    ui.render()

if __name__ == "__main__":
    main()
