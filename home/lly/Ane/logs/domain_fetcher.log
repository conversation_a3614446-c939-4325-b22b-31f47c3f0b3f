2025-07-11 20:36:53,275 - DomainFetcher - INFO - 开始获取 1 个域名的源码
2025-07-11 20:37:22,402 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-11 20:37:25,538 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 2 个JS, 0 个CSS, 0 个图片
2025-07-11 20:37:25,613 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-11 22:14:40,276 - DomainFetcher - INFO - 开始获取 1 个域名的源码
2025-07-11 22:14:45,764 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-11 22:14:48,882 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-11 22:14:48,883 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-11 22:14:48,958 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-11 22:45:05,832 - DomainFetcher - INFO - 开始获取 1 个域名的源码
2025-07-11 22:45:10,539 - DomainFetcher - ERROR - 执行失败: 'WebDriver' object has no attribute 'set_page_load_strategy'
Traceback (most recent call last):
  File "/home/<USER>/Ane/src/steps/domain_fetcher.py", line 79, in execute
    results = self._fetch_with_browser(cleaned_domains)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Ane/src/steps/domain_fetcher.py", line 208, in _fetch_with_browser
    driver.set_page_load_strategy("normal")
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'WebDriver' object has no attribute 'set_page_load_strategy'. Did you mean: 'set_page_load_timeout'?
2025-07-11 23:04:10,444 - DomainFetcher - INFO - 开始获取 1 个域名的源码
2025-07-11 23:04:12,908 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-11 23:04:14,030 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-11 23:04:14,031 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-11 23:04:14,104 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-17 09:29:57,025 - DomainFetcher - INFO - 开始获取 1 个域名的源码
2025-07-17 09:30:35,662 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-17 09:38:08,267 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-17 09:38:32,951 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-17 09:38:58,743 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-17 10:35:23,816 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 10:35:27,468 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-17 10:35:27,551 - DomainFetcher - ERROR - 获取 http://localhost:8080/ 失败: 浏览器错误: Message: unknown error: net::ERR_CONNECTION_REFUSED
  (Session info: chrome=133.0.6943.53)
Stacktrace:
#0 0x63f9e8a7014a <unknown>
#1 0x63f9e850db80 <unknown>
#2 0x63f9e8504d89 <unknown>
#3 0x63f9e84f5039 <unknown>
#4 0x63f9e84f6d3d <unknown>
#5 0x63f9e84f53ce <unknown>
#6 0x63f9e84f4d6e <unknown>
#7 0x63f9e84f4a1f <unknown>
#8 0x63f9e84f2762 <unknown>
#9 0x63f9e84f302a <unknown>
#10 0x63f9e8511019 <unknown>
#11 0x63f9e85abe15 <unknown>
#12 0x63f9e8584ed2 <unknown>
#13 0x63f9e85ab14a <unknown>
#14 0x63f9e8584ca3 <unknown>
#15 0x63f9e8550f08 <unknown>
#16 0x63f9e8552071 <unknown>
#17 0x63f9e8a39b5b <unknown>
#18 0x63f9e8a3dae2 <unknown>
#19 0x63f9e8a25967 <unknown>
#20 0x63f9e8a3e6d4 <unknown>
#21 0x63f9e8a09c7f <unknown>
#22 0x63f9e8a5ecd8 <unknown>
#23 0x63f9e8a5eea9 <unknown>
#24 0x63f9e8a6efc6 <unknown>
#25 0x7990cea9caa4 <unknown>
#26 0x7990ceb29c3c <unknown>

2025-07-17 10:35:27,570 - DomainFetcher - WARNING - 错误后仍获取到部分内容，长度: 254421
2025-07-17 10:35:27,645 - DomainFetcher - INFO - 成功获取 0/1 个域名的源码
2025-07-17 10:36:03,760 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 10:36:06,180 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-17 10:36:06,253 - DomainFetcher - ERROR - 获取 http://localhost:8080/ 失败: 浏览器错误: Message: unknown error: net::ERR_CONNECTION_REFUSED
  (Session info: chrome=133.0.6943.53)
Stacktrace:
#0 0x5762c9a4014a <unknown>
#1 0x5762c94ddb80 <unknown>
#2 0x5762c94d4d89 <unknown>
#3 0x5762c94c5039 <unknown>
#4 0x5762c94c6d3d <unknown>
#5 0x5762c94c53ce <unknown>
#6 0x5762c94c4d6e <unknown>
#7 0x5762c94c4a1f <unknown>
#8 0x5762c94c2762 <unknown>
#9 0x5762c94c302a <unknown>
#10 0x5762c94e1019 <unknown>
#11 0x5762c957be15 <unknown>
#12 0x5762c9554ed2 <unknown>
#13 0x5762c957b14a <unknown>
#14 0x5762c9554ca3 <unknown>
#15 0x5762c9520f08 <unknown>
#16 0x5762c9522071 <unknown>
#17 0x5762c9a09b5b <unknown>
#18 0x5762c9a0dae2 <unknown>
#19 0x5762c99f5967 <unknown>
#20 0x5762c9a0e6d4 <unknown>
#21 0x5762c99d9c7f <unknown>
#22 0x5762c9a2ecd8 <unknown>
#23 0x5762c9a2eea9 <unknown>
#24 0x5762c9a3efc6 <unknown>
#25 0x7d3d0a89caa4 <unknown>
#26 0x7d3d0a929c3c <unknown>

2025-07-17 10:36:06,272 - DomainFetcher - WARNING - 错误后仍获取到部分内容，长度: 254421
2025-07-17 10:36:06,347 - DomainFetcher - INFO - 成功获取 0/1 个域名的源码
2025-07-17 10:36:35,371 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 10:36:38,412 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-17 10:36:45,146 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-17 10:36:45,147 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-17 10:36:45,207 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-17 11:09:07,290 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 11:09:09,623 - DomainFetcher - INFO - 正在访问: http://localhost:8080/
2025-07-17 11:09:10,737 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:09:10,738 - DomainFetcher - INFO - 成功获取 http://localhost:8080/ 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:09:10,809 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-17 11:17:06,795 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 11:17:09,668 - DomainFetcher - INFO - 正在访问: https://httpbin.org/html
2025-07-17 11:17:11,468 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:17:11,469 - DomainFetcher - INFO - 成功获取 https://httpbin.org/html 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:17:11,540 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
2025-07-17 11:17:41,608 - DomainFetcher - INFO - 开始获取 1 个域名的源码，使用方法: browser
2025-07-17 11:17:43,220 - DomainFetcher - INFO - 正在访问: https://httpbin.org/html
2025-07-17 11:17:45,779 - DomainFetcher - INFO - 捕获到 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:17:45,779 - DomainFetcher - INFO - 成功获取 https://httpbin.org/html 的源码，包含 0 个JS, 0 个CSS, 0 个图片
2025-07-17 11:17:45,850 - DomainFetcher - INFO - 成功获取 1/1 个域名的源码
