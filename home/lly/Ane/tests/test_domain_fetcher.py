#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名获取器单元测试
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.steps.domain_fetcher import DomainFetcher


class TestDomainFetcher(unittest.TestCase):
    """域名获取器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.fetcher = DomainFetcher('test', '域名获取测试', '测试域名获取功能')
    
    def test_validate_params_valid(self):
        """测试有效参数验证"""
        valid_params = {
            'domains': ['http://example.com'],
            'method': 'requests'
        }
        self.assertTrue(self.fetcher.validate_params(valid_params))
    
    def test_validate_params_invalid(self):
        """测试无效参数验证"""
        # 缺少domains
        invalid_params1 = {'method': 'requests'}
        self.assertFalse(self.fetcher.validate_params(invalid_params1))
        
        # domains为空
        invalid_params2 = {'domains': [], 'method': 'requests'}
        self.assertFalse(self.fetcher.validate_params(invalid_params2))
        
        # 无效的method
        invalid_params3 = {'domains': ['http://example.com'], 'method': 'invalid'}
        self.assertFalse(self.fetcher.validate_params(invalid_params3))
    
    def test_normalize_url(self):
        """测试URL标准化"""
        # 测试各种URL格式
        test_cases = [
            ('example.com', 'http://example.com'),
            ('http://example.com', 'http://example.com'),
            ('https://example.com', 'https://example.com'),
            ('www.example.com', 'http://www.example.com'),
            ('example.com/path', 'http://example.com/path'),
        ]
        
        for input_url, expected in test_cases:
            result = self.fetcher._normalize_url(input_url)
            self.assertEqual(result, expected, f"URL标准化失败: {input_url} -> {result}, 期望: {expected}")
    
    def test_extract_resources_from_html(self):
        """测试从HTML提取资源"""
        html_content = '''
        <!DOCTYPE html>
        <html>
        <head>
            <script src="/js/main.js"></script>
            <script src="https://cdn.example.com/lib.js"></script>
            <link rel="stylesheet" href="/css/style.css">
            <link rel="stylesheet" href="https://cdn.example.com/theme.css">
        </head>
        <body>
            <img src="/images/logo.png" alt="Logo">
            <img src="https://cdn.example.com/banner.jpg" alt="Banner">
        </body>
        </html>
        '''
        
        base_url = 'http://example.com'
        resources = self.fetcher._extract_resources_from_html(html_content, base_url)
        
        # 检查提取的资源
        self.assertIn('scripts', resources)
        self.assertIn('styles', resources)
        self.assertIn('images', resources)
        
        # 检查JavaScript文件
        js_files = resources['scripts']
        self.assertEqual(len(js_files), 2)
        js_urls = [js['url'] for js in js_files]
        self.assertIn('http://example.com/js/main.js', js_urls)
        self.assertIn('https://cdn.example.com/lib.js', js_urls)
        
        # 检查CSS文件
        css_files = resources['styles']
        self.assertEqual(len(css_files), 2)
        css_urls = [css['url'] for css in css_files]
        self.assertIn('http://example.com/css/style.css', css_urls)
        self.assertIn('https://cdn.example.com/theme.css', css_urls)
        
        # 检查图片文件
        img_files = resources['images']
        self.assertEqual(len(img_files), 2)
        img_urls = [img['url'] for img in img_files]
        self.assertIn('http://example.com/images/logo.png', img_urls)
        self.assertIn('https://cdn.example.com/banner.jpg', img_urls)
    
    def test_merge_resources(self):
        """测试资源合并"""
        resources1 = {
            'scripts': [{'url': 'http://example.com/js1.js', 'filename': 'js1.js'}],
            'styles': [{'url': 'http://example.com/css1.css', 'filename': 'css1.css'}]
        }
        
        resources2 = {
            'scripts': [{'url': 'http://example.com/js2.js', 'filename': 'js2.js'}],
            'images': [{'url': 'http://example.com/img1.png', 'filename': 'img1.png'}]
        }
        
        merged = self.fetcher._merge_resources(resources1, resources2)
        
        # 检查合并结果
        self.assertEqual(len(merged['scripts']), 2)
        self.assertEqual(len(merged['styles']), 1)
        self.assertEqual(len(merged['images']), 1)
        
        # 检查去重功能
        resources3 = {
            'scripts': [{'url': 'http://example.com/js1.js', 'filename': 'js1.js'}]  # 重复
        }
        
        merged_with_dup = self.fetcher._merge_resources(merged, resources3)
        self.assertEqual(len(merged_with_dup['scripts']), 2)  # 应该去重
    
    def test_clean_and_validate_ip(self):
        """测试IP地址验证"""
        # 有效IP
        valid_ips = ['***********', '********', '**********', '*******']
        for ip in valid_ips:
            self.assertTrue(self.fetcher._validate_ip(ip), f"有效IP验证失败: {ip}")
        
        # 无效IP
        invalid_ips = ['256.1.1.1', '192.168.1', '***********.1', 'not.an.ip']
        for ip in invalid_ips:
            self.assertFalse(self.fetcher._validate_ip(ip), f"无效IP验证失败: {ip}")


if __name__ == '__main__':
    unittest.main()
