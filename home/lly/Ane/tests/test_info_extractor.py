#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息提取器单元测试
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.steps.info_extractor import InfoExtractor


class TestInfoExtractor(unittest.TestCase):
    """信息提取器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.extractor = InfoExtractor('test', '信息提取测试', '测试信息提取功能')
    
    def test_extract_phone_numbers(self):
        """测试手机号码提取"""
        test_code = '''
        var phone1 = '13812345678';
        var phone2 = "15987654321";
        var invalid = '12345678901';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到2个有效手机号
        self.assertIn('mobile', extracted_info)
        self.assertEqual(len(extracted_info['mobile']), 2)
        self.assertIn('13812345678', extracted_info['mobile'])
        self.assertIn('15987654321', extracted_info['mobile'])
    
    def test_extract_id_cards(self):
        """测试身份证号码提取"""
        test_code = '''
        var id1 = '110101199001011234';  // 18位身份证
        var id2 = "320102199001011234";  // 18位身份证
        var invalid = '123456789012345';  // 无效格式
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到2个有效身份证号
        self.assertIn('sfz', extracted_info)
        self.assertEqual(len(extracted_info['sfz']), 2)
        self.assertIn('110101199001011234', extracted_info['sfz'])
        self.assertIn('320102199001011234', extracted_info['sfz'])
    
    def test_extract_emails(self):
        """测试邮箱地址提取"""
        test_code = '''
        var email1 = '<EMAIL>';
        var email2 = "<EMAIL>";
        var invalid = 'not-an-email';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到2个邮箱
        self.assertIn('mail', extracted_info)
        self.assertEqual(len(extracted_info['mail']), 2)
        self.assertIn('<EMAIL>', extracted_info['mail'])
        self.assertIn('<EMAIL>', extracted_info['mail'])
    
    def test_extract_jwt_tokens(self):
        """测试JWT Token提取"""
        test_code = '''
        var token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到1个JWT Token
        self.assertIn('jwt', extracted_info)
        self.assertEqual(len(extracted_info['jwt']), 1)
        self.assertTrue(extracted_info['jwt'][0].startswith('eyJ'))
    
    def test_extract_api_keys(self):
        """测试API密钥提取"""
        test_code = '''
        var apiKey = 'sk-1234567890abcdefghijklmnopqrstuvwxyz';
        var publicKey = 'pk_live_51H7qABC123456789';
        var secret = 'my-super-secret-key-2023';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到密钥
        self.assertIn('secret', extracted_info)
        self.assertGreater(len(extracted_info['secret']), 0)
    
    def test_extract_crypto_algorithms(self):
        """测试加密算法提取"""
        test_code = '''
        var encrypted = CryptoJS.AES.encrypt(data, key);
        var hash = md5(password);
        var encoded = btoa(data);
        var sha = sha256(input);
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到加密算法
        self.assertIn('algorithm', extracted_info)
        self.assertGreater(len(extracted_info['algorithm']), 0)
        
        # 检查具体算法
        algorithms = extracted_info['algorithm']
        self.assertTrue(any('CryptoJS.AES' in alg for alg in algorithms))
        self.assertTrue(any('md5' in alg for alg in algorithms))
        self.assertTrue(any('btoa' in alg for alg in algorithms))
    
    def test_extract_js_imports(self):
        """测试JavaScript导入路径提取"""
        test_code = '''
        import{r as o,aN as U,z as k,co as x,C as A,j as l,bp as D,bD as L,bq as P,b7 as B,br as j}from"./index.C1NIn1Y2.js";
        import{u as _}from"./uniqueId.O0UbJ2Bu.js";
        import{u as q,a as w,b as K}from"./useOnInputChange.Cxh6ExEn.js";
        import{I as N}from"./InputInstructions.DaZ89mzH.js";
        import{a as O}from"./useBasicWidgetState.Ci89jaH5.js";
        import{T as $}from"./textarea.QKjxR64N.js";
        import"./inputUtils.CQWz5UKz.js";
        import"./FormClearHelper.D1M9GM_c.js";
        import"./base-input.BJ4qsfSq.js";
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到JavaScript导入路径
        self.assertIn('js_imports', extracted_info)
        self.assertGreater(len(extracted_info['js_imports']), 0)
        
        # 检查具体路径
        imports = extracted_info['js_imports']
        expected_imports = [
            './index.C1NIn1Y2.js',
            './uniqueId.O0UbJ2Bu.js',
            './useOnInputChange.Cxh6ExEn.js',
            './InputInstructions.DaZ89mzH.js',
            './useBasicWidgetState.Ci89jaH5.js',
            './textarea.QKjxR64N.js',
            './inputUtils.CQWz5UKz.js',
            './FormClearHelper.D1M9GM_c.js',
            './base-input.BJ4qsfSq.js'
        ]
        
        for expected in expected_imports:
            self.assertIn(expected, imports, f"应该提取到导入路径: {expected}")
    
    def test_extract_ip_addresses(self):
        """测试IP地址提取"""
        test_code = '''
        var server1 = '*************';
        var server2 = "********";
        var serverWithPort = '*************:8080';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到IP地址
        self.assertIn('ip', extracted_info)
        self.assertGreater(len(extracted_info['ip']), 0)
        
        # 应该提取到IP:端口
        self.assertIn('ip_port', extracted_info)
        self.assertGreater(len(extracted_info['ip_port']), 0)
    
    def test_extract_urls_and_domains(self):
        """测试URL和域名提取"""
        test_code = '''
        var api = 'https://api.example.com/v1/users';
        var website = "http://www.test.com";
        var domain = 'example.org';
        '''
        
        params = {'content': test_code, 'filename': 'test.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 应该提取到URL
        self.assertIn('url', extracted_info)
        self.assertGreater(len(extracted_info['url']), 0)
        
        # 应该提取到域名
        self.assertIn('domain', extracted_info)
        self.assertGreater(len(extracted_info['domain']), 0)
    
    def test_empty_content(self):
        """测试空内容"""
        params = {'content': '', 'filename': 'empty.js'}
        result = self.extractor.execute(params)
        
        self.assertTrue(result['success'])
        extracted_info = result['data']['extracted_info']
        
        # 所有类型都应该为空
        for info_type, items in extracted_info.items():
            self.assertEqual(len(items), 0, f"{info_type} 应该为空")
    
    def test_invalid_params(self):
        """测试无效参数"""
        # 缺少content参数
        result = self.extractor.execute({'filename': 'test.js'})
        self.assertFalse(result['success'])
        
        # 缺少filename参数
        result = self.extractor.execute({'content': 'test'})
        self.assertTrue(result['success'])  # filename是可选的


if __name__ == '__main__':
    unittest.main()
