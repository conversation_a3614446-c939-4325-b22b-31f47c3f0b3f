# Ane项目单元测试

这个目录包含了Ane项目的所有单元测试。

## 目录结构

```
tests/
├── __init__.py                 # 测试包初始化
├── README.md                   # 测试说明文档
├── test_info_extractor.py      # 信息提取器测试
├── test_domain_fetcher.py      # 域名获取器测试
├── test_code_analyzer.py       # 代码分析器测试
├── test_code_splitter.py       # 代码拆分器测试
├── test_vulnerability_audit.py # 漏洞审计器测试
├── test_workflow_manager.py    # 工作流管理器测试
├── test_database_manager.py    # 数据库管理器测试
└── fixtures/                   # 测试数据和固定装置
    ├── sample_html.html
    ├── sample_js.js
    └── sample_css.css
```

## 运行测试

### 运行所有测试
```bash
cd /home/<USER>/Ane
python -m pytest tests/ -v
```

### 运行特定测试文件
```bash
python -m pytest tests/test_info_extractor.py -v
```

### 运行特定测试方法
```bash
python -m pytest tests/test_info_extractor.py::TestInfoExtractor::test_extract_phone_numbers -v
```

### 生成测试覆盖率报告
```bash
python -m pytest tests/ --cov=src --cov-report=html
```

## 测试规范

1. 所有测试文件以 `test_` 开头
2. 测试类以 `Test` 开头
3. 测试方法以 `test_` 开头
4. 使用描述性的测试名称
5. 每个测试应该独立，不依赖其他测试
6. 使用适当的断言和异常测试
7. 为复杂功能提供多个测试用例

## 测试数据

测试数据存放在 `fixtures/` 目录中，包括：
- 示例HTML文件
- 示例JavaScript文件
- 示例CSS文件
- 测试用的敏感信息样本
