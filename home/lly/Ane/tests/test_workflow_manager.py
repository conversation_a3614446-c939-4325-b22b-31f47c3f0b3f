#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作流管理器单元测试
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.workflow_manager import WorkflowManager


class TestWorkflowManager(unittest.TestCase):
    """工作流管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.workflow_manager = WorkflowManager()
    
    def test_initialization(self):
        """测试初始化"""
        # 检查步骤是否正确初始化
        expected_steps = [
            'domain_fetcher',
            'framework_analyzer', 
            'code_analyzer',
            'code_splitter',
            'vulnerability_audit',
            'info_extractor'
        ]
        
        for step_id in expected_steps:
            self.assertIn(step_id, self.workflow_manager.steps)
            self.assertIsNotNone(self.workflow_manager.steps[step_id])
    
    def test_get_workflow_status(self):
        """测试获取工作流状态"""
        status = self.workflow_manager.get_workflow_status()
        
        # 检查状态结构
        self.assertIn('total_steps', status)
        self.assertIn('completed_steps', status)
        self.assertIn('failed_steps', status)
        self.assertIn('step_details', status)
        
        # 检查数据类型
        self.assertIsInstance(status['total_steps'], int)
        self.assertIsInstance(status['completed_steps'], int)
        self.assertIsInstance(status['failed_steps'], int)
        self.assertIsInstance(status['step_details'], dict)
    
    def test_get_step_info(self):
        """测试获取步骤信息"""
        # 测试有效步骤
        step_info = self.workflow_manager.get_step_info('domain_fetcher')
        self.assertIsNotNone(step_info)
        self.assertIn('step_id', step_info)
        self.assertIn('name', step_info)
        self.assertIn('description', step_info)
        
        # 测试无效步骤
        invalid_step_info = self.workflow_manager.get_step_info('invalid_step')
        self.assertIsNone(invalid_step_info)
    
    def test_run_step_info_extractor(self):
        """测试运行信息提取步骤"""
        params = {
            'content': '''
            var apiKey = 'sk-1234567890abcdefghijklmnopqrstuvwxyz';
            var userPhone = '13812345678';
            var userEmail = '<EMAIL>';
            ''',
            'filename': 'test.js'
        }
        
        result = self.workflow_manager.run_step('info_extractor', params)
        
        # 检查结果结构
        self.assertIn('success', result)
        self.assertIn('message', result)
        self.assertIn('data', result)
        
        # 检查成功执行
        self.assertTrue(result['success'])
        self.assertIsNotNone(result['data'])
        
        # 检查提取结果
        extracted_info = result['data']['extracted_info']
        self.assertIn('secret', extracted_info)
        self.assertIn('mobile', extracted_info)
        self.assertIn('mail', extracted_info)
    
    def test_run_step_invalid(self):
        """测试运行无效步骤"""
        result = self.workflow_manager.run_step('invalid_step', {})
        
        self.assertFalse(result['success'])
        self.assertIn('error', result['message'].lower())
    
    def test_run_step_invalid_params(self):
        """测试运行步骤时参数无效"""
        # 信息提取器需要content参数
        result = self.workflow_manager.run_step('info_extractor', {})
        
        self.assertFalse(result['success'])
        self.assertIn('参数验证失败', result['message'])


if __name__ == '__main__':
    unittest.main()
