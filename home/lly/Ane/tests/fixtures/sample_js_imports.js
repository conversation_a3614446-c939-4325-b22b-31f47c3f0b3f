// 现代JavaScript打包后的导入示例
import{r as o,aN as U,z as k,co as x,C as A,j as l,bp as D,bD as L,bq as P,b7 as B,br as j}from"./index.C1NIn1Y2.js";
import{u as _}from"./uniqueId.O0UbJ2Bu.js";
import{u as q,a as w,b as K}from"./useOnInputChange.Cxh6ExEn.js";
import{I as N}from"./InputInstructions.DaZ89mzH.js";
import{a as O}from"./useBasicWidgetState.Ci89jaH5.js";
import{T as $}from"./textarea.QKjxR64N.js";
import"./inputUtils.CQWz5UKz.js";
import"./FormClearHelper.D1M9GM_c.js";
import"./base-input.BJ4qsfSq.js";

// 其他导入格式
import React from 'react';
import { useState, useEffect } from 'react';
import * as utils from './utils/helpers.js';
import './styles/main.css';

// CommonJS格式
const express = require('express');
const path = require('path');
const fs = require('fs');

// 动态导入
const module = await import('./dynamic-module.js');

// 包含敏感信息的代码
const config = {
    apiKey: 'sk-1234567890abcdefghijklmnopqrstuvwxyz',
    dbHost: '*************:3306',
    userEmail: '<EMAIL>',
    userPhone: '13812345678',
    jwtSecret: 'my-jwt-secret-key-2023'
};

// 加密算法使用
const encrypted = CryptoJS.AES.encrypt(data, config.apiKey);
const hash = md5(password);
const encoded = btoa(JSON.stringify(config));
