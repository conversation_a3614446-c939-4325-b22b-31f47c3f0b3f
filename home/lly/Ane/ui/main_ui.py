#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版主UI界面 - 支持资源文件浏览
"""

import streamlit as st
import pandas as pd
import json
import requests
from typing import Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go

class MainUI:
    """简化版主UI类"""
    
    def __init__(self, workflow_manager):
        self.workflow_manager = workflow_manager
    
    def render(self):
        """渲染主界面"""
        st.title("🔍 Ane - Web Analyzer")
        st.markdown("---")
        
        # 使用展开式导航菜单
        with st.sidebar:
            st.header("🔍 Ane - Web Analyzer")
            st.markdown("---")

            # 初始化选中页面
            if 'selected_page' not in st.session_state:
                st.session_state.selected_page = "工作流管理"

            # 工作流管理 - 默认展开
            with st.expander("📋 工作流管理", expanded=True):
                if st.button("🏠 工作流概览", use_container_width=True):
                    st.session_state.selected_page = "工作流管理"
                    st.rerun()
                if st.button("▶️ 步骤执行", use_container_width=True):
                    st.session_state.selected_page = "步骤执行"
                    st.rerun()

            # 结果查看
            with st.expander("📊 结果查看", expanded=False):
                if st.button("📈 查看结果", use_container_width=True):
                    st.session_state.selected_page = "结果查看"
                    st.rerun()
                if st.button("📊 数据统计", use_container_width=True):
                    st.session_state.selected_page = "数据统计"
                    st.rerun()

            # 系统设置
            with st.expander("⚙️ 系统设置", expanded=False):
                if st.button("🔧 设置", use_container_width=True):
                    st.session_state.selected_page = "设置"
                    st.rerun()

            selected = st.session_state.selected_page
        
        # 根据选择渲染对应页面
        if selected == "工作流管理":
            self._render_workflow_management()
        elif selected == "步骤执行":
            self._render_step_execution()
        elif selected == "结果查看":
            self._render_results_view()
        elif selected == "数据统计":
            self._render_statistics()
        elif selected == "设置":
            self._render_settings()
    
    def _render_workflow_management(self):
        """渲染工作流管理页面"""
        st.header("📋 工作流管理")
        
        # 工作流状态
        status = self.workflow_manager.get_workflow_status()
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("状态", status['status'].upper())
        with col2:
            st.metric("总步骤", status['total_steps'])
        with col3:
            st.metric("已完成", status['completed_steps'])
        with col4:
            progress = status['completed_steps'] / status['total_steps'] if status['total_steps'] > 0 else 0
            st.metric("进度", f"{progress:.1%}")
        
        # 可用步骤列表
        st.subheader("可用步骤")
        steps = self.workflow_manager.get_available_steps()
        
        for step_id, step_info in steps.items():
            with st.expander(f"📝 {step_info['name']}", expanded=False):
                st.write(f"**描述**: {step_info['description']}")
                st.write(f"**状态**: {step_info['status']}")
                st.write(f"**可运行**: {'✅' if step_info['can_run'] else '❌'}")
                st.write(f"**结果数**: {step_info['results_count']}")
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button(f"查看结果", key=f"view_{step_id}", disabled=step_info['results_count'] == 0):
                        # 设置session state来跳转到结果查看页面
                        st.session_state.selected_page = "结果查看"
                        if step_id == 'domain_fetcher':
                            st.session_state.result_type = "域名获取结果"
                        elif step_id == 'framework_analyzer':
                            st.session_state.result_type = "框架分析结果"
                        st.rerun()
                with col2:
                    if st.button(f"清空结果", key=f"clear_{step_id}"):
                        self.workflow_manager.clear_step_results(step_id)
                        st.success(f"已清空 {step_info['name']} 的结果")
                        st.rerun()
        
        # 执行整个工作流
        st.markdown("---")
        st.subheader("工作流执行")
        domains_input = st.text_area(
            "输入要分析的域名列表（每行一个）",
            height=150,
            value="http://localhost:8080/",
            placeholder="http://localhost:8080/\nexample.com\nwww.google.com",
            key="workflow_domains"
        )
        
        if st.button("🚀 执行整个工作流", type="primary"):
            if not domains_input.strip():
                st.error("请输入至少一个域名")
                return
            
            domains = [line.strip() for line in domains_input.split('\n') if line.strip()]
            params = {
                'domain_fetcher': {
                    'domains': domains,
                    'method': 'browser'
                }
            }
            
            with st.spinner('正在执行工作流...'):
                try:
                    result = self.workflow_manager.run_entire_workflow(params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        domain_result = result['results']['domain_fetcher']
                        framework_result = result['results']['framework_analyzer']
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("域名获取成功", domain_result['success_count'])
                        with col2:
                            st.metric("域名获取失败", domain_result['total'] - domain_result['success_count'])
                        with col3:
                            st.metric("框架分析完成", framework_result['analyzed_count'])
                    else:
                        st.error(f"❌ {result['message']}")
                except Exception as e:
                    st.error(f"工作流执行失败: {str(e)}")
        
        # 结果快速查看
        st.markdown("---")
        st.subheader("📊 结果快速查看")
        self._render_quick_results_view()

        # 重置工作流
        st.markdown("---")
        if st.button("🔄 重置整个工作流", type="secondary"):
            if st.session_state.get('confirm_reset', False):
                self.workflow_manager.reset_workflow()
                st.success("工作流已重置")
                st.session_state.confirm_reset = False
                st.rerun()
            else:
                st.session_state.confirm_reset = True
                st.warning("请再次点击确认重置")
    
    def _render_step_execution(self):
        """渲染步骤执行页面"""
        st.header("▶️ 步骤执行")
        
        # 选择步骤
        steps = self.workflow_manager.get_available_steps()
        step_options = {f"{info['name']}": step_id 
                       for step_id, info in steps.items()}
        
        selected_step_name = st.selectbox("选择要执行的步骤", list(step_options.keys()))
        selected_step_id = step_options[selected_step_name]
        
        # 根据步骤显示对应的参数输入界面
        if selected_step_id == 'domain_fetcher':
            self._render_domain_fetcher_params()
        elif selected_step_id == 'framework_analyzer':
            self._render_framework_analyzer_params()
        else:
            st.info("该步骤暂未实现参数配置界面")
    
    def _render_domain_fetcher_params(self):
        """渲染域名获取器参数界面"""
        st.subheader("🌐 域名源码获取配置")
        
        # 域名输入
        domains_input = st.text_area(
            "输入域名列表（每行一个）",
            height=150,
            value="http://localhost:8080/",
            placeholder="http://localhost:8080/\nexample.com\nwww.google.com"
        )
        
        # 获取方法选择
        method = st.selectbox(
            "选择获取方法",
            ["browser", "browser-use"],
            help="browser: 使用Selenium浏览器获取\nbrowser-use: 使用更智能的浏览器自动化（需要安装browser-use）"
        )
        
        # 执行按钮
        if st.button("🚀 开始获取", type="primary"):
            if not domains_input.strip():
                st.error("请输入至少一个域名")
                return
            
            # 解析域名列表
            domains = [line.strip() for line in domains_input.split('\n') if line.strip()]
            
            # 显示进度
            with st.spinner('正在获取域名源码...'):
                try:
                    # 执行步骤
                    params = {'domains': domains, 'method': method}
                    result = self.workflow_manager.run_step('domain_fetcher', params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("总计", result['total'])
                        with col2:
                            st.metric("成功", result['success_count'])
                        with col3:
                            st.metric("失败", result['total'] - result['success_count'])
                        
                        # 保存到数据库
                        self.workflow_manager.db_manager.save_domain_results(result['data'])
                    else:
                        st.error(f"❌ {result['message']}")
                
                except Exception as e:
                    st.error(f"执行失败: {str(e)}")
    
    def _render_framework_analyzer_params(self):
        """渲染框架分析器参数界面"""
        st.subheader("🔧 前端框架分析配置")
        
        # 检查是否有域名获取结果
        domain_results = self.workflow_manager.get_step_results('domain_fetcher')
        
        if not domain_results:
            st.warning("⚠️ 请先执行域名源码获取步骤")
            return
        
        # 显示可分析的域名数量
        successful_results = [r for r in domain_results if r.get('success', False)]
        st.info(f"找到 {len(successful_results)} 个成功获取源码的域名可供分析")
        
        # 执行按钮
        if st.button("🔍 开始分析", type="primary"):
            with st.spinner('正在分析前端框架...'):
                try:
                    # 执行分析
                    params = {'source_data': successful_results}
                    result = self.workflow_manager.run_step('framework_analyzer', params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("总计", len(successful_results))
                        with col2:
                            st.metric("已分析", result['analyzed_count'])
                        
                        # 保存到数据库
                        self.workflow_manager.db_manager.save_framework_analysis(result['data'])
                    else:
                        st.error(f"❌ {result['message']}")
                
                except Exception as e:
                    st.error(f"分析失败: {str(e)}")
    
    def _render_results_view(self):
        """渲染结果查看页面"""
        st.header("📊 结果查看")

        # 选择查看的结果类型
        default_result_type = "域名获取结果"
        if 'result_type' in st.session_state:
            default_result_type = st.session_state.result_type
            # 清除session state，避免影响后续操作
            del st.session_state.result_type

        result_types = ["域名获取结果", "框架分析结果"]
        default_index = result_types.index(default_result_type) if default_result_type in result_types else 0

        result_type = st.selectbox(
            "选择结果类型",
            result_types,
            index=default_index
        )
        
        if result_type == "域名获取结果":
            self._render_domain_results()
        elif result_type == "框架分析结果":
            self._render_framework_results()
    
    def _render_domain_results(self):
        """渲染域名获取结果 - 使用树形结构"""
        results = self.workflow_manager.db_manager.get_domain_results(limit=100)

        if not results:
            st.info("暂无域名获取结果")
            return

        # 转换为DataFrame
        df = pd.DataFrame(results)

        # 过滤选项
        col1, col2 = st.columns(2)
        with col1:
            status_filter = st.selectbox("状态过滤", ["全部", "成功", "失败"], key="results_status_filter")
        with col2:
            limit = st.number_input("显示数量", min_value=10, max_value=1000, value=50, key="results_limit")

        # 应用过滤
        if status_filter != "全部":
            df = df[df['status'] == ('success' if status_filter == '成功' else 'failed')]

        df = df.head(limit)

        # 显示数据表
        if not df.empty:
            st.dataframe(
                df[['domain', 'status', 'status_code', 'response_time', 'content_length', 'created_at']],
                use_container_width=True
            )

            # 域名选择
            st.subheader("📁 文件结构浏览")
            domain_options = [f"{row['domain']} ({'✅' if row['status'] == 'success' else '❌'})"
                             for _, row in df.iterrows()]

            selected_domain_display = st.selectbox(
                "选择域名查看文件结构",
                domain_options,
                key="results_domain_select"
            )

            if selected_domain_display:
                # 获取选中的域名数据
                selected_index = domain_options.index(selected_domain_display)
                selected_row = df.iloc[selected_index]

                # 构建文件树并显示
                self._render_results_file_tree(selected_row)

            # 导出功能
            st.subheader("📥 数据导出")
            if st.button("导出数据", key="export_results"):
                csv = df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    "下载 CSV 文件",
                    csv,
                    file_name=f"domain_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv",
                    key="download_results_csv"
                )
        else:
            st.info("没有符合条件的结果")
    
    def _render_framework_results(self):
        """渲染框架分析结果"""
        results = self.workflow_manager.db_manager.get_framework_analysis(limit=100)
        
        if not results:
            st.info("暂无框架分析结果")
            return
        
        # 显示结果列表
        for result in results[:20]:  # 限制显示数量
            with st.expander(f"🌐 {result['domain']}", expanded=False):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**检测到的框架:**")
                    if result['frameworks']:
                        for framework, info in result['frameworks'].items():
                            confidence = info.get('confidence', 0)
                            st.write(f"- {framework}: {confidence}% 置信度")
                    else:
                        st.write("未检测到主要框架")
                
                with col2:
                    st.write("**Source Map:**")
                    if result['has_sourcemap']:
                        st.write("✅ 发现 Source Map 文件")
                        for url in result['sourcemap_urls']:
                            st.write(f"- {url}")
                    else:
                        st.write("❌ 未发现 Source Map 文件")
    
    def _render_statistics(self):
        """渲染统计页面"""
        st.header("📈 数据统计")
        
        # 获取统计数据
        try:
            stats = self.workflow_manager.db_manager.get_statistics()
            
            # 基本统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总域名数", stats['total_domains'])
            with col2:
                st.metric("成功获取", stats['successful_domains'])
            with col3:
                st.metric("已分析", stats['analyzed_domains'])
            with col4:
                st.metric("含Source Map", stats['sourcemap_count'])
            
            # 成功率图表
            if stats['total_domains'] > 0:
                success_rate = stats['success_rate']
                fig = go.Figure(go.Indicator(
                    mode = "gauge+number",
                    value = success_rate,
                    title = {'text': "获取成功率 (%)"},
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 80], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 90
                        }
                    }
                ))
                st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            st.error(f"获取统计数据失败: {str(e)}")
    
    def _render_settings(self):
        """渲染设置页面"""
        st.header("⚙️ 系统设置")
        
        # 数据管理
        st.subheader("数据管理")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📁 导出所有数据"):
                try:
                    export_path = self.workflow_manager.db_manager.export_to_json()
                    st.success(f"数据已导出到: {export_path}")
                except Exception as e:
                    st.error(f"导出失败: {str(e)}")
        
        with col2:
            if st.button("🗑️ 清空所有数据", type="secondary"):
                if st.session_state.get('confirm_clear', False):
                    self.workflow_manager.db_manager.clear_all_data()
                    st.success("所有数据已清空")
                    st.session_state.confirm_clear = False
                    st.rerun()
                else:
                    st.session_state.confirm_clear = True
                    st.warning("请再次点击确认清空")
        
        # 系统信息
        st.subheader("系统信息")
        system_info = {
            "数据库路径": self.workflow_manager.db_manager.db_path,
            "可用步骤数": len(self.workflow_manager.steps),
            "工作流状态": self.workflow_manager.status.value
        }
        
        for key, value in system_info.items():
            st.write(f"**{key}**: {value}")

    def _render_quick_results_view(self):
        """渲染快速结果查看 - 显示历史数据"""
        # 从数据库获取历史数据
        domain_results = self.workflow_manager.db_manager.get_domain_results(limit=20)
        framework_results = self.workflow_manager.get_step_results('framework_analyzer')  # 框架分析结果暂时从内存获取

        if not domain_results and not framework_results:
            st.info("暂无结果数据")
            return

        # 选择查看类型
        view_type = st.selectbox(
            "选择查看类型",
            ["域名获取结果", "框架分析结果"],
            key="quick_view_type"
        )

        if view_type == "域名获取结果" and domain_results:
            # 转换数据库结果格式为快速查看格式
            converted_results = self._convert_db_results_to_quick_format(domain_results)
            self._render_quick_domain_results(converted_results)
        elif view_type == "框架分析结果" and framework_results:
            self._render_quick_framework_results(framework_results)
        else:
            st.info(f"暂无{view_type}数据")

    def _convert_db_results_to_quick_format(self, db_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将数据库结果转换为快速查看格式"""
        converted_results = []

        for db_result in db_results:
            # 转换数据库格式到内存格式
            converted_result = {
                'domain': db_result.get('domain', ''),
                'success': db_result.get('status') == 'success',
                'source_code': db_result.get('source_code', ''),
                'response_time': db_result.get('response_time', 0),
                'status_code': db_result.get('status_code', 0),
                'content_length': db_result.get('content_length', 0),
                'final_url': db_result.get('final_url', ''),
                'error': db_result.get('error_message', ''),
                'created_at': db_result.get('created_at', ''),
                'resources': db_result.get('resources', {})
            }

            # 确保resources是字典格式
            if isinstance(converted_result['resources'], str):
                try:
                    converted_result['resources'] = json.loads(converted_result['resources'])
                except (json.JSONDecodeError, TypeError):
                    converted_result['resources'] = {}
            elif not isinstance(converted_result['resources'], dict):
                converted_result['resources'] = {}

            converted_results.append(converted_result)

        return converted_results

    def _render_quick_domain_results(self, results: List[Dict[str, Any]]):
        """快速查看域名获取结果 - 文件树结构"""
        # 过滤成功的结果
        successful_results = [r for r in results if r.get('success', False)]
        failed_results = [r for r in results if not r.get('success', False)]

        # 显示统计信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("总计", len(results))
        with col2:
            st.metric("成功", len(successful_results))
        with col3:
            st.metric("失败", len(failed_results))

        if not successful_results:
            st.warning("没有成功获取的结果")
            return

        # 选择要查看的域名
        domain_options = [f"{r['domain']} ({'成功' if r.get('success') else '失败'})"
                         for r in results[:10]]  # 限制显示数量

        selected_domain_display = st.selectbox(
            "选择域名查看详情",
            domain_options,
            key="quick_domain_select"
        )

        if selected_domain_display:
            # 获取选中的结果
            selected_index = domain_options.index(selected_domain_display)
            selected_result = results[selected_index]

            # 显示基本信息
            st.subheader(f"🌐 {selected_result['domain']}")

            col1, col2, col3 = st.columns(3)
            with col1:
                st.write(f"**状态**: {'✅ 成功' if selected_result.get('success') else '❌ 失败'}")
            with col2:
                st.write(f"**响应时间**: {selected_result.get('response_time', 0):.2f}s")
            with col3:
                st.write(f"**内容长度**: {selected_result.get('content_length', 0)}")

            # 文件树结构显示
            self._render_quick_file_tree(selected_result)

            # 显示错误信息（如果有）
            if not selected_result.get('success') and selected_result.get('error'):
                st.subheader("❌ 错误信息")
                st.error(selected_result['error'])

    def _render_quick_file_tree(self, result: Dict[str, Any]):
        """渲染快速查看的文件树结构"""
        st.subheader("📁 文件结构")

        # 构建文件树
        file_tree = {}

        # 添加主HTML文件
        if result.get('source_code'):
            domain_name = result['domain'].replace('https://', '').replace('http://', '').replace('/', '_')
            file_tree[f"{domain_name}.html"] = {
                'type': 'html',
                'content': result['source_code'],
                'size': len(result['source_code'])
            }

        # 添加资源文件
        resources = result.get('resources', {})
        for resource_type, resource_list in resources.items():
            if isinstance(resource_list, list):
                for resource in resource_list:
                    filename = resource.get('filename', 'unknown')
                    if not filename or filename == 'unknown':
                        url = resource.get('url', '')
                        filename = url.split('/')[-1] if '/' in url else 'unknown'

                    file_tree[filename] = {
                        'type': resource_type,
                        'url': resource.get('url', ''),
                        'content': resource.get('content'),  # 内容已经获取
                        'size': resource.get('size', 0),
                        'fetch_success': resource.get('fetch_success'),
                        'fetch_error': resource.get('fetch_error')
                    }

        # 显示文件树
        if not file_tree:
            st.info("没有找到文件")
            return

        # 使用简单的文件树显示
        self._render_simple_file_tree(file_tree)

    def _render_file_tree(self, result: Dict[str, Any]):
        """渲染文件树结构"""
        st.subheader("📁 文件结构")

        # 构建文件树
        file_tree = {}

        # 添加主HTML文件
        if result.get('source_code'):
            domain_name = result['domain'].replace('https://', '').replace('http://', '').replace('/', '_')
            file_tree[f"{domain_name}.html"] = {
                'type': 'html',
                'content': result['source_code'],
                'size': len(result['source_code'])
            }

        # 添加资源文件
        resources = result.get('resources', {})
        for resource_type, resource_list in resources.items():
            for resource in resource_list:
                filename = resource.get('filename', 'unknown')
                if not filename or filename == 'unknown':
                    url = resource.get('url', '')
                    filename = url.split('/')[-1] if '/' in url else 'unknown'

                file_tree[filename] = {
                    'type': resource_type,
                    'url': resource.get('url', ''),
                    'content': None,  # 需要时再获取
                    'size': 0
                }

        # 显示文件树
        if not file_tree:
            st.info("没有找到文件")
            return

        # 构建层次化的文件树
        tree_structure = self._build_tree_structure(file_tree)

        # 渲染文件树
        self._render_tree_structure(tree_structure, file_tree)

    def _render_file_content(self, filename: str, file_info: Dict[str, Any]):
        """渲染文件内容"""
        st.subheader(f"📄 {filename}")

        content = file_info.get('content')

        # 如果是HTML文件且有内容
        if file_info['type'] == 'html' and content:
            self._display_code_content(content, 'html', filename)

        # 如果是资源文件，自动获取内容
        elif file_info.get('url') and not content:
            with st.spinner(f'正在获取 {filename} 内容...'):
                try:
                    import requests
                    response = requests.get(file_info['url'], timeout=15, headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    })
                    if response.status_code == 200:
                        content = response.text
                        file_info['content'] = content
                        file_info['size'] = len(content)

                        # 根据文件类型确定语言
                        language = self._get_language_from_type(file_info['type'], filename)
                        self._display_code_content(content, language, filename)
                    else:
                        st.error(f"❌ 获取失败: HTTP {response.status_code}")
                except Exception as e:
                    st.error(f"❌ 获取内容失败: {str(e)}")

        # 如果已有内容
        elif content:
            language = self._get_language_from_type(file_info['type'], filename)
            self._display_code_content(content, language, filename)

        else:
            st.info("暂无内容或需要手动获取")

    def _display_code_content(self, content: str, language: str, filename: str):
        """显示代码内容，支持行数限制和滚动"""
        if not content:
            st.info("内容为空")
            return

        # 内容统计
        lines = content.split('\n')
        total_lines = len(lines)

        col1, col2 = st.columns(2)
        with col1:
            st.info(f"总行数: {total_lines}")
        with col2:
            st.info(f"字符数: {len(content)}")

        # 行数显示控制
        max_lines = st.number_input(
            "显示行数",
            min_value=100,
            max_value=min(10000, total_lines),
            value=min(2000, total_lines),
            step=100,
            key=f"lines_{filename}"
        )

        # 起始行控制
        start_line = st.number_input(
            "起始行",
            min_value=1,
            max_value=max(1, total_lines - max_lines + 1),
            value=1,
            key=f"start_{filename}"
        )

        # 显示内容
        end_line = min(start_line + max_lines - 1, total_lines)
        display_lines = lines[start_line-1:end_line]
        display_content = '\n'.join(display_lines)

        st.code(display_content, language=language)

        # 下载选项
        st.download_button(
            f"📥 下载 {filename}",
            content,
            file_name=filename,
            mime=self._get_mime_type(language)
        )

    def _get_language_from_type(self, file_type: str, filename: str) -> str:
        """根据文件类型和文件名确定语言"""
        # 优先根据文件扩展名判断
        if filename.endswith(('.js', '.mjs', '.jsx')):
            return 'javascript'
        elif filename.endswith(('.css', '.scss', '.sass', '.less')):
            return 'css'
        elif filename.endswith(('.html', '.htm')):
            return 'html'
        elif filename.endswith('.json'):
            return 'json'
        elif filename.endswith(('.xml', '.svg')):
            return 'xml'
        elif filename.endswith(('.ts', '.tsx')):
            return 'typescript'
        elif filename.endswith('.vue'):
            return 'vue'
        elif filename.endswith('.py'):
            return 'python'

        # 根据文件类型判断
        elif file_type in ['scripts', 'js', 'javascript']:
            return 'javascript'
        elif file_type in ['styles', 'css']:
            return 'css'
        elif file_type == 'html':
            return 'html'
        else:
            return 'text'

    def _get_mime_type(self, language: str) -> str:
        """根据语言获取MIME类型"""
        mime_types = {
            'html': 'text/html',
            'javascript': 'application/javascript',
            'css': 'text/css',
            'json': 'application/json',
            'xml': 'application/xml'
        }
        return mime_types.get(language, 'text/plain')

    def _handle_code_analysis(self, filename: str, file_info: Dict[str, Any]):
        """处理代码分析"""
        content = file_info.get('content')
        if not content:
            st.error("无法获取文件内容进行分析")
            return

        with st.spinner(f'正在分析 {filename}...'):
            try:
                params = {
                    'file_content': content,
                    'filename': filename
                }

                result = self.workflow_manager.run_step('code_analyzer', params)

                if result['success']:
                    st.success(f"✅ {result['message']}")

                    # 显示分析结果
                    analysis_data = result['data']
                    self._display_analysis_result(analysis_data)
                else:
                    st.error(f"❌ {result['message']}")

            except Exception as e:
                st.error(f"分析失败: {str(e)}")

    def _handle_code_splitting(self, filename: str, file_info: Dict[str, Any]):
        """处理代码拆分"""
        content = file_info.get('content')
        if not content:
            st.error("无法获取文件内容进行拆分")
            return

        # AI模型选择和API密钥输入
        col1, col2 = st.columns(2)
        with col1:
            ai_model = st.selectbox("选择AI模型", ["deepseek", "minimax"], key=f"ai_model_{filename}")
        with col2:
            api_key = st.text_input("API密钥", type="password", key=f"api_key_{filename}")

        if not api_key:
            st.warning("请输入API密钥")
            return

        if st.button(f"开始拆分 {filename}", key=f"start_split_{filename}"):
            with st.spinner(f'正在拆分 {filename}...'):
                try:
                    params = {
                        'file_content': content,
                        'filename': filename,
                        'ai_model': ai_model,
                        'api_key': api_key
                    }

                    result = self.workflow_manager.run_step('code_splitter', params)

                    if result['success']:
                        st.success(f"✅ {result['message']}")

                        # 显示拆分结果
                        split_data = result['data']
                        self._display_split_result(split_data)
                    else:
                        st.error(f"❌ {result['message']}")

                except Exception as e:
                    st.error(f"拆分失败: {str(e)}")

    def _handle_vulnerability_audit(self, filename: str, file_info: Dict[str, Any]):
        """处理漏洞审计"""
        content = file_info.get('content')
        if not content:
            st.error("无法获取文件内容进行审计")
            return

        # AI模型选择和API密钥输入
        col1, col2 = st.columns(2)
        with col1:
            ai_model = st.selectbox("选择AI模型", ["deepseek", "minimax"], key=f"audit_ai_model_{filename}")
        with col2:
            api_key = st.text_input("API密钥", type="password", key=f"audit_api_key_{filename}")

        if not api_key:
            st.warning("请输入API密钥")
            return

        if st.button(f"开始审计 {filename}", key=f"start_audit_{filename}"):
            with st.spinner(f'正在审计 {filename}...'):
                try:
                    params = {
                        'file_content': content,
                        'filename': filename,
                        'ai_model': ai_model,
                        'api_key': api_key
                    }

                    result = self.workflow_manager.run_step('vulnerability_audit', params)

                    if result['success']:
                        st.success(f"✅ {result['message']}")

                        # 显示审计结果
                        audit_data = result['data']
                        self._display_audit_result(audit_data)
                    else:
                        st.error(f"❌ {result['message']}")

                except Exception as e:
                    st.error(f"审计失败: {str(e)}")

    def _display_analysis_result(self, analysis_data: Dict[str, Any]):
        """显示代码分析结果"""
        st.subheader("🔍 代码分析结果")

        # 基本信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"**文件类型**: {analysis_data.get('file_type', 'unknown')}")
        with col2:
            frameworks = analysis_data.get('frameworks', {})
            st.write(f"**检测到框架**: {len(frameworks)}")
        with col3:
            bundlers = analysis_data.get('bundlers', {})
            st.write(f"**构建工具**: {len(bundlers)}")

        # 框架详情
        if frameworks:
            st.subheader("🎯 检测到的框架")
            for framework, info in frameworks.items():
                confidence = info.get('confidence', 0)
                st.write(f"**{framework}**: {confidence}% 置信度")

        # 构建工具详情
        if bundlers:
            st.subheader("📦 构建工具")
            for bundler, info in bundlers.items():
                confidence = info.get('confidence', 0)
                st.write(f"**{bundler}**: {confidence}% 置信度")

        # 代码指标
        metrics = analysis_data.get('code_metrics', {})
        if metrics:
            st.subheader("📊 代码指标")
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总行数", metrics.get('total_lines', 0))
            with col2:
                st.metric("代码行数", metrics.get('code_lines', 0))
            with col3:
                st.metric("函数数量", metrics.get('function_count', 0))
            with col4:
                st.metric("导入数量", metrics.get('import_count', 0))

    def _display_split_result(self, split_data: Dict[str, Any]):
        """显示代码拆分结果"""
        st.subheader("✂️ 代码拆分结果")

        split_result = split_data.get('split_result', {})

        # 获取拆分的文件
        from src.steps.code_splitter import CodeSplitter
        splitter = CodeSplitter('temp', 'temp', 'temp')
        files = splitter.get_split_files(split_result)

        if files:
            st.success(f"成功拆分为 {len(files)} 个文件")

            # 显示文件列表
            for i, file_info in enumerate(files):
                with st.expander(f"📄 {file_info['filename']}", expanded=False):
                    st.write(f"**路径**: {file_info['path']}")
                    st.write(f"**类型**: {file_info['type']}")

                    if file_info.get('dependencies'):
                        st.write(f"**依赖**: {', '.join(file_info['dependencies'])}")

                    # 显示内容
                    content = file_info.get('content', '')
                    if content:
                        st.code(content[:2000], language=file_info['type'])

                        # 下载按钮
                        st.download_button(
                            f"📥 下载 {file_info['filename']}",
                            content,
                            file_name=file_info['filename'],
                            key=f"download_split_{i}"
                        )
        else:
            st.warning("未能成功拆分文件")

    def _display_audit_result(self, audit_data: Dict[str, Any]):
        """显示漏洞审计结果"""
        st.subheader("🛡️ 漏洞审计结果")

        audit_result = audit_data.get('audit_result', {})
        summary = audit_result.get('summary', {})

        # 摘要信息
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("总问题数", summary.get('total_issues', 0))
        with col2:
            st.metric("高风险", summary.get('high_risk', 0))
        with col3:
            st.metric("中风险", summary.get('medium_risk', 0))
        with col4:
            st.metric("安全评分", f"{summary.get('overall_score', 0)}/10")

        # 漏洞详情
        vulnerabilities = audit_result.get('vulnerabilities', [])
        if vulnerabilities:
            st.subheader("🚨 发现的漏洞")

            for i, vuln in enumerate(vulnerabilities):
                severity = vuln.get('severity', 'LOW')
                severity_color = {
                    'HIGH': '🔴',
                    'MEDIUM': '🟡',
                    'LOW': '🟢'
                }.get(severity, '⚪')

                with st.expander(f"{severity_color} {vuln.get('title', '未知漏洞')}", expanded=False):
                    st.write(f"**类型**: {vuln.get('type', 'unknown')}")
                    st.write(f"**严重程度**: {severity}")
                    st.write(f"**描述**: {vuln.get('description', '')}")

                    location = vuln.get('location', {})
                    if location.get('line'):
                        st.write(f"**位置**: 第 {location['line']} 行")
                    if location.get('code'):
                        st.code(location['code'], language='javascript')

                    st.write(f"**影响**: {vuln.get('impact', '')}")
                    st.write(f"**修复建议**: {vuln.get('recommendation', '')}")

        # 最佳实践建议
        best_practices = audit_result.get('best_practices', [])
        if best_practices:
            st.subheader("💡 安全最佳实践建议")
            for practice in best_practices:
                st.write(f"- {practice}")

    def _render_quick_framework_results(self, results: List[Dict[str, Any]]):
        """快速查看框架分析结果"""
        if not results:
            st.info("暂无框架分析结果")
            return

        # 显示统计信息
        total_analyzed = len(results)
        has_frameworks = len([r for r in results if r.get('frameworks')])
        has_sourcemap = len([r for r in results if r.get('has_sourcemap')])

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("已分析", total_analyzed)
        with col2:
            st.metric("检测到框架", has_frameworks)
        with col3:
            st.metric("含Source Map", has_sourcemap)

        # 选择要查看的域名
        domain_options = [r['domain'] for r in results[:10]]  # 限制显示数量

        selected_domain = st.selectbox(
            "选择域名查看分析结果",
            domain_options,
            key="quick_framework_select"
        )

        if selected_domain:
            # 获取选中的结果
            selected_result = next(r for r in results if r['domain'] == selected_domain)

            st.subheader(f"🔧 {selected_domain} - 框架分析")

            # 显示检测到的框架
            frameworks = selected_result.get('frameworks', {})
            if frameworks:
                st.subheader("🎯 检测到的框架")
                for framework, info in frameworks.items():
                    confidence = info.get('confidence', 0)
                    st.write(f"**{framework}**: {confidence}% 置信度")

                    # 显示证据
                    evidence = info.get('evidence', [])
                    if evidence:
                        with st.expander(f"查看 {framework} 检测证据", expanded=False):
                            for ev in evidence:
                                st.write(f"- {ev}")
            else:
                st.info("未检测到主要前端框架")

            # 显示Source Map信息
            st.subheader("🗺️ Source Map 信息")
            if selected_result.get('has_sourcemap'):
                st.success("✅ 发现 Source Map 文件")
                sourcemap_urls = selected_result.get('sourcemap_urls', [])
                if sourcemap_urls:
                    st.write("**Source Map URLs:**")
                    for url in sourcemap_urls:
                        st.write(f"- {url}")
            else:
                st.info("❌ 未发现 Source Map 文件")

            # 显示打包工具信息
            bundler = selected_result.get('bundler', 'unknown')
            st.subheader("📦 打包工具")
            if bundler != 'unknown':
                st.write(f"检测到打包工具: **{bundler}**")
            else:
                st.info("未检测到明确的打包工具")

    def _build_tree_structure(self, file_tree: Dict[str, Any]) -> Dict[str, Any]:
        """构建层次化的文件树结构"""
        tree = {}

        for filename, file_info in file_tree.items():
            # 解析文件路径
            if file_info.get('url'):
                # 从URL中提取路径
                from urllib.parse import urlparse
                parsed_url = urlparse(file_info['url'])
                path_parts = parsed_url.path.strip('/').split('/')

                # 如果路径为空，使用域名作为根目录
                if not path_parts or path_parts == ['']:
                    path_parts = [parsed_url.netloc, filename]
                elif path_parts[-1] != filename:
                    path_parts.append(filename)
            else:
                # 本地文件，使用简单的分类
                file_type = file_info.get('type', 'other')
                if file_type == 'html':
                    path_parts = ['/', filename]
                elif file_type == 'scripts':
                    path_parts = ['/', 'js', filename]
                elif file_type == 'styles':
                    path_parts = ['/', 'css', filename]
                elif file_type == 'images':
                    path_parts = ['/', 'images', filename]
                else:
                    path_parts = ['/', 'other', filename]

            # 构建树结构
            current = tree
            for i, part in enumerate(path_parts):
                if part not in current:
                    current[part] = {
                        '_is_file': i == len(path_parts) - 1,
                        '_file_info': file_info if i == len(path_parts) - 1 else None,
                        '_path': '/'.join(path_parts[:i+1])
                    }
                current = current[part]

        return tree

    def _render_tree_structure(self, tree: Dict[str, Any], file_tree: Dict[str, Any], level: int = 0):
        """渲染紧凑的树状结构"""
        for name, node in tree.items():
            if name.startswith('_'):  # 跳过元数据
                continue

            if node.get('_is_file', False):
                # 这是一个文件
                file_info = node.get('_file_info')
                if file_info:
                    # 文件图标
                    file_type = file_info.get('type', 'other')
                    icon = self._get_file_icon(file_type)

                    # 使用更紧凑的文件显示，增强层次感
                    with st.container():
                        # 添加背景色和边框增强层次感
                        st.markdown("""
                        <div style="background-color: #f8f9fa; padding: 8px; border-radius: 5px; border-left: 3px solid #007bff; margin: 5px 0;">
                        """, unsafe_allow_html=True)

                        # 文件名和操作按钮在同一行
                        col1, col2 = st.columns([3, 2])

                        with col1:
                            # 缩进和文件名
                            indent = "├─ " if level > 0 else ""
                            st.write(f"{indent}{icon} **{name}**")

                            # 显示URL（如果有）
                            if file_info.get('url'):
                                st.caption(f"🔗 {file_info['url'][:50]}{'...' if len(file_info['url']) > 50 else ''}")

                        with col2:
                            # 操作按钮 - 紧凑排列，带文字标签
                            file_hash = hash(f"{name}_{file_info.get('url', '')}")

                            # 使用更紧凑的按钮布局
                            btn_col1, btn_col2, btn_col3, btn_col4 = st.columns(4)

                            with btn_col1:
                                if st.button("👁️", key=f"view_{file_hash}", help="查看内容"):
                                    st.session_state[f'show_file_{file_hash}'] = not st.session_state.get(f'show_file_{file_hash}', False)
                                    st.rerun()
                                st.caption("查看")

                            with btn_col2:
                                if st.button("🔍", key=f"analyze_{file_hash}", help="分析代码"):
                                    self._handle_code_analysis(name, file_info)
                                st.caption("分析")

                            with btn_col3:
                                if st.button("✂️", key=f"split_{file_hash}", help="拆分代码"):
                                    self._handle_code_splitting(name, file_info)
                                st.caption("拆分")

                            with btn_col4:
                                if st.button("🛡️", key=f"audit_{file_hash}", help="漏洞审计"):
                                    self._handle_vulnerability_audit(name, file_info)
                                st.caption("审计")

                        st.markdown("</div>", unsafe_allow_html=True)

                        # 如果被选中显示，则展开内容
                        if st.session_state.get(f'show_file_{file_hash}', False):
                            # 内容区域有更明显的层次感
                            st.markdown("""
                            <div style="background-color: #ffffff; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6; margin: 10px 0;">
                            """, unsafe_allow_html=True)

                            self._render_file_content(name, file_info)

                            st.markdown("</div>", unsafe_allow_html=True)
            else:
                # 这是一个目录
                folder_icon = "📁"
                indent = "├─ " if level > 0 else ""
                st.write(f"{indent}{folder_icon} **{name}/**")

                # 递归渲染子目录
                self._render_tree_structure(node, file_tree, level + 1)

    def _get_file_icon(self, file_type: str) -> str:
        """根据文件类型获取图标"""
        icons = {
            'html': '🌐',
            'js': '📜',
            'javascript': '📜',
            'scripts': '📜',
            'css': '🎨',
            'styles': '🎨',
            'image': '🖼️',
            'images': '🖼️',
            'json': '📋',
            'xml': '📄',
            'other': '📄'
        }
        return icons.get(file_type, '📄')

    def _render_file_details(self, filename: str, file_info: Dict[str, Any]):
        """渲染文件详细信息和操作 - 紧凑版"""
        # 文件基本信息 - 更紧凑的布局
        col1, col2 = st.columns([3, 1])
        with col1:
            st.write(f"**{filename}** ({file_info.get('type', 'unknown')})")
            if file_info.get('url'):
                st.caption(f"🔗 {file_info['url']}")
        with col2:
            if file_info.get('size'):
                st.metric("大小", f"{file_info['size']} 字符")

        # 操作按钮 - 水平排列，更紧凑，带标签
        st.markdown("**操作选项：**")
        col1, col2, col3 = st.columns(3)

        file_hash = hash(f"{filename}_{file_info.get('url', '')}_details")

        with col1:
            if st.button("🔍 分析", key=f"detail_analyze_{file_hash}", use_container_width=True):
                self._handle_code_analysis(filename, file_info)

        with col2:
            if st.button("✂️ 拆分", key=f"detail_split_{file_hash}", use_container_width=True):
                self._handle_code_splitting(filename, file_info)

        with col3:
            if st.button("🛡️ 审计", key=f"detail_audit_{file_hash}", use_container_width=True):
                self._handle_vulnerability_audit(filename, file_info)

        # 内容显示
        st.markdown("---")
        self._render_file_content(filename, file_info)

    def _render_results_file_tree(self, domain_row):
        """渲染结果查看界面的文件树"""
        # 构建文件树
        file_tree = {}

        # 添加主HTML文件
        if domain_row.get('source_code'):
            domain_name = domain_row['domain'].replace('https://', '').replace('http://', '').replace('/', '_')
            file_tree[f"{domain_name}.html"] = {
                'type': 'html',
                'content': domain_row['source_code'],
                'size': len(domain_row['source_code']),
                'url': domain_row['domain']
            }

        # 添加资源文件
        resources = domain_row.get('resources', {})
        if isinstance(resources, str):
            try:
                resources = json.loads(resources)
            except:
                resources = {}

        for resource_type, resource_list in resources.items():
            if isinstance(resource_list, list):
                for resource in resource_list:
                    filename = resource.get('filename', 'unknown')
                    if not filename or filename == 'unknown':
                        url = resource.get('url', '')
                        filename = url.split('/')[-1] if '/' in url else 'unknown'

                    file_tree[filename] = {
                        'type': resource_type,
                        'url': resource.get('url', ''),
                        'content': None,  # 需要时再获取
                        'size': 0
                    }

        # 显示文件树
        if not file_tree:
            st.info("没有找到文件")
            return

        # 使用简单的树形结构显示
        self._render_simple_file_tree(file_tree)

    def _build_compact_tree_structure(self, file_tree: Dict[str, Any]) -> Dict[str, Any]:
        """构建紧凑的文件树结构"""
        tree = {
            'html': {},
            'scripts': {},
            'styles': {},
            'images': {},
            'other': {}
        }

        for filename, file_info in file_tree.items():
            file_type = file_info.get('type', 'other')

            # 根据类型分类
            if file_type == 'html':
                tree['html'][filename] = file_info
            elif file_type in ['scripts', 'js', 'javascript']:
                tree['scripts'][filename] = file_info
            elif file_type in ['styles', 'css']:
                tree['styles'][filename] = file_info
            elif file_type in ['images', 'image']:
                tree['images'][filename] = file_info
            else:
                tree['other'][filename] = file_info

        # 移除空的分类
        return {k: v for k, v in tree.items() if v}

    def _render_compact_tree_structure(self, tree: Dict[str, Dict], file_tree: Dict[str, Any]):
        """渲染紧凑的树状结构"""
        type_icons = {
            'html': '🌐',
            'scripts': '📜',
            'styles': '🎨',
            'images': '🖼️',
            'other': '📄'
        }

        type_names = {
            'html': 'HTML 文件',
            'scripts': 'JavaScript 文件',
            'styles': 'CSS 文件',
            'images': '图片文件',
            'other': '其他文件'
        }

        for file_type, files in tree.items():
            if not files:
                continue

            icon = type_icons.get(file_type, '📄')
            type_name = type_names.get(file_type, '其他文件')

            # 使用紧凑的expander显示文件类型
            with st.expander(f"{icon} {type_name} ({len(files)})", expanded=file_type == 'html'):
                for filename, file_info in files.items():
                    # 使用更紧凑的文件显示
                    self._render_compact_file_item(filename, file_info)

    def _render_compact_file_item(self, filename: str, file_info: Dict[str, Any]):
        """渲染紧凑的文件项 - 增强层次感"""
        # 添加背景和边框增强层次感
        st.markdown("""
        <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; border-left: 3px solid #28a745; margin: 8px 0;">
        """, unsafe_allow_html=True)

        # 文件名和操作按钮在同一行
        col1, col2 = st.columns([2, 3])

        with col1:
            # 文件名和URL
            st.write(f"**{filename}**")
            if file_info.get('url'):
                st.caption(f"🔗 {file_info['url'][:40]}{'...' if len(file_info['url']) > 40 else ''}")

        with col2:
            # 操作按钮 - 紧凑排列，带标签
            btn_col1, btn_col2, btn_col3, btn_col4 = st.columns(4)

            file_hash = hash(f"{filename}_{file_info.get('url', '')}_compact")

            with btn_col1:
                if st.button("👁️", key=f"compact_view_{file_hash}", help="查看内容"):
                    self._show_file_content_modal(filename, file_info)
                st.caption("查看")

            with btn_col2:
                if st.button("🔍", key=f"compact_analyze_{file_hash}", help="分析代码"):
                    self._handle_code_analysis(filename, file_info)
                st.caption("分析")

            with btn_col3:
                if st.button("✂️", key=f"compact_split_{file_hash}", help="拆分代码"):
                    self._handle_code_splitting(filename, file_info)
                st.caption("拆分")

            with btn_col4:
                if st.button("🛡️", key=f"compact_audit_{file_hash}", help="漏洞审计"):
                    self._handle_vulnerability_audit(filename, file_info)
                st.caption("审计")

        st.markdown("</div>", unsafe_allow_html=True)

    def _show_file_content_modal(self, filename: str, file_info: Dict[str, Any]):
        """显示文件内容模态框"""
        st.subheader(f"📄 {filename}")

        # 文件信息
        col1, col2, col3 = st.columns(3)
        with col1:
            st.write(f"**类型**: {file_info.get('type', 'unknown')}")
        with col2:
            st.write(f"**大小**: {file_info.get('size', 'Unknown')}")
        with col3:
            if file_info.get('url'):
                st.write(f"**来源**: 远程文件")
            else:
                st.write(f"**来源**: 本地文件")

        # 获取并显示内容
        content = file_info.get('content')

        if content:
            # 已有内容，直接显示
            language = self._get_language_from_type(file_info.get('type', ''), filename)
            self._display_code_content(content, language, filename)
        elif file_info.get('url'):
            # 需要获取远程内容
            if st.button(f"📥 获取 {filename} 内容", key=f"fetch_content_{hash(filename)}"):
                with st.spinner(f'正在获取 {filename} 内容...'):
                    try:
                        import requests
                        response = requests.get(file_info['url'], timeout=10)
                        if response.status_code == 200:
                            content = response.text
                            file_info['content'] = content
                            file_info['size'] = len(content)

                            # 显示内容
                            language = self._get_language_from_type(file_info.get('type', ''), filename)
                            self._display_code_content(content, language, filename)
                        else:
                            st.error(f"获取失败: HTTP {response.status_code}")
                    except Exception as e:
                        st.error(f"获取内容失败: {str(e)}")
        else:
            st.info("暂无内容")

        # 操作按钮 - 紧凑布局
        if content or file_info.get('url'):
            st.markdown("---")
            st.markdown("**🔧 操作选项**")
            col1, col2, col3 = st.columns(3)

            file_hash = hash(f"{filename}_{file_info.get('url', '')}_modal")

            with col1:
                if st.button("🔍 分析", key=f"modal_analyze_{file_hash}", use_container_width=True):
                    self._handle_code_analysis(filename, file_info)

            with col2:
                if st.button("✂️ 拆分", key=f"modal_split_{file_hash}", use_container_width=True):
                    self._handle_code_splitting(filename, file_info)

            with col3:
                if st.button("🛡️ 审计", key=f"modal_audit_{file_hash}", use_container_width=True):
                    self._handle_vulnerability_audit(filename, file_info)

    def _render_simple_file_tree(self, file_tree: Dict[str, Any]):
        """渲染简单的文件树结构"""
        if not file_tree:
            st.info("没有找到文件")
            return

        # 按类型分组文件
        grouped_files = {
            'HTML 文件': [],
            'JavaScript 文件': [],
            'CSS 文件': [],
            '图片文件': [],
            '其他文件': []
        }

        for filename, file_info in file_tree.items():
            file_type = file_info.get('type', 'other')

            if file_type == 'html':
                grouped_files['HTML 文件'].append((filename, file_info))
            elif file_type in ['scripts', 'js', 'javascript']:
                grouped_files['JavaScript 文件'].append((filename, file_info))
            elif file_type in ['styles', 'css']:
                grouped_files['CSS 文件'].append((filename, file_info))
            elif file_type in ['images', 'image']:
                grouped_files['图片文件'].append((filename, file_info))
            else:
                grouped_files['其他文件'].append((filename, file_info))

        # 显示文件树
        for group_name, files in grouped_files.items():
            if not files:
                continue

            # 文件类型图标
            type_icons = {
                'HTML 文件': '🌐',
                'JavaScript 文件': '📜',
                'CSS 文件': '🎨',
                '图片文件': '🖼️',
                '其他文件': '📄'
            }

            icon = type_icons.get(group_name, '📄')

            # 使用expander显示文件组
            with st.expander(f"{icon} {group_name} ({len(files)})", expanded=group_name in ['HTML 文件', 'JavaScript 文件']):
                for filename, file_info in files:
                    self._render_tree_file_item(filename, file_info)

    def _render_tree_file_item(self, filename: str, file_info: Dict[str, Any]):
        """渲染树形结构中的文件项"""
        # 创建带背景的容器
        with st.container():
            st.markdown("""
            <div style="background-color: #f8f9fa; padding: 8px; border-radius: 5px; border-left: 3px solid #007bff; margin: 5px 0;">
            """, unsafe_allow_html=True)

            # 文件信息和操作按钮
            col1, col2 = st.columns([2, 3])

            with col1:
                # 文件名和基本信息
                st.write(f"**{filename}**")

                # 显示文件状态
                if file_info.get('content'):
                    st.success(f"✅ 已加载 ({len(file_info['content'])} 字符)")
                elif file_info.get('fetch_success') is False:
                    st.error(f"❌ 加载失败: {file_info.get('fetch_error', '未知错误')}")
                elif file_info.get('url'):
                    st.info("🔗 远程文件")
                else:
                    st.info("📄 本地文件")

                # URL信息
                if file_info.get('url'):
                    st.caption(f"🔗 {file_info['url'][:50]}{'...' if len(file_info['url']) > 50 else ''}")

            with col2:
                # 操作按钮 - 紧凑排列
                btn_col1, btn_col2, btn_col3, btn_col4 = st.columns(4)

                file_hash = hash(f"{filename}_{file_info.get('url', '')}_tree")

                with btn_col1:
                    if st.button("👁️", key=f"tree_view_{file_hash}", help="查看内容"):
                        st.session_state[f'show_tree_content_{file_hash}'] = not st.session_state.get(f'show_tree_content_{file_hash}', False)
                        st.rerun()
                    st.caption("查看")

                with btn_col2:
                    if st.button("🔍", key=f"tree_analyze_{file_hash}", help="分析代码"):
                        self._handle_code_analysis(filename, file_info)
                    st.caption("分析")

                with btn_col3:
                    if st.button("✂️", key=f"tree_split_{file_hash}", help="拆分代码"):
                        self._handle_code_splitting(filename, file_info)
                    st.caption("拆分")

                with btn_col4:
                    if st.button("🛡️", key=f"tree_audit_{file_hash}", help="漏洞审计"):
                        self._handle_vulnerability_audit(filename, file_info)
                    st.caption("审计")

            st.markdown("</div>", unsafe_allow_html=True)

            # 内容显示区域
            if st.session_state.get(f'show_tree_content_{file_hash}', False):
                st.markdown("""
                <div style="background-color: #ffffff; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6; margin: 10px 0;">
                """, unsafe_allow_html=True)

                # 显示文件内容
                content = file_info.get('content')
                if content:
                    language = self._get_language_from_type(file_info.get('type', ''), filename)
                    self._display_code_content(content, language, filename)
                else:
                    st.info("暂无内容或内容获取失败")

                st.markdown("</div>", unsafe_allow_html=True)
