#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版主UI界面 - 支持资源文件浏览
"""

import streamlit as st
import pandas as pd
import json
import requests
from typing import Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go

class MainUI:
    """简化版主UI类"""
    
    def __init__(self, workflow_manager):
        self.workflow_manager = workflow_manager
    
    def render(self):
        """渲染主界面"""
        st.title("🔍 Ane - Web Analyzer")
        st.markdown("---")
        
        # 使用简单的selectbox代替option_menu
        with st.sidebar:
            st.header("导航菜单")

            # 检查是否有session state设置的页面跳转
            default_index = 0
            if 'selected_page' in st.session_state:
                pages = ["工作流管理", "步骤执行", "结果查看", "数据统计", "设置"]
                if st.session_state.selected_page in pages:
                    default_index = pages.index(st.session_state.selected_page)

            selected = st.selectbox(
                "选择功能页面",
                ["工作流管理", "步骤执行", "结果查看", "数据统计", "设置"],
                index=default_index
            )
        
        # 根据选择渲染对应页面
        if selected == "工作流管理":
            self._render_workflow_management()
        elif selected == "步骤执行":
            self._render_step_execution()
        elif selected == "结果查看":
            self._render_results_view()
        elif selected == "数据统计":
            self._render_statistics()
        elif selected == "设置":
            self._render_settings()
    
    def _render_workflow_management(self):
        """渲染工作流管理页面"""
        st.header("📋 工作流管理")
        
        # 工作流状态
        status = self.workflow_manager.get_workflow_status()
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("状态", status['status'].upper())
        with col2:
            st.metric("总步骤", status['total_steps'])
        with col3:
            st.metric("已完成", status['completed_steps'])
        with col4:
            progress = status['completed_steps'] / status['total_steps'] if status['total_steps'] > 0 else 0
            st.metric("进度", f"{progress:.1%}")
        
        # 可用步骤列表
        st.subheader("可用步骤")
        steps = self.workflow_manager.get_available_steps()
        
        for step_id, step_info in steps.items():
            with st.expander(f"📝 {step_info['name']}", expanded=False):
                st.write(f"**描述**: {step_info['description']}")
                st.write(f"**状态**: {step_info['status']}")
                st.write(f"**可运行**: {'✅' if step_info['can_run'] else '❌'}")
                st.write(f"**结果数**: {step_info['results_count']}")
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button(f"查看结果", key=f"view_{step_id}", disabled=step_info['results_count'] == 0):
                        # 设置session state来跳转到结果查看页面
                        st.session_state.selected_page = "结果查看"
                        if step_id == 'domain_fetcher':
                            st.session_state.result_type = "域名获取结果"
                        elif step_id == 'framework_analyzer':
                            st.session_state.result_type = "框架分析结果"
                        st.rerun()
                with col2:
                    if st.button(f"清空结果", key=f"clear_{step_id}"):
                        self.workflow_manager.clear_step_results(step_id)
                        st.success(f"已清空 {step_info['name']} 的结果")
                        st.rerun()
        
        # 执行整个工作流
        st.markdown("---")
        st.subheader("工作流执行")
        domains_input = st.text_area(
            "输入要分析的域名列表（每行一个）",
            height=150,
            placeholder="example.com\nwww.google.com\nhttps://github.com",
            key="workflow_domains"
        )
        
        if st.button("🚀 执行整个工作流", type="primary"):
            if not domains_input.strip():
                st.error("请输入至少一个域名")
                return
            
            domains = [line.strip() for line in domains_input.split('\n') if line.strip()]
            params = {
                'domain_fetcher': {
                    'domains': domains,
                    'method': 'browser'
                }
            }
            
            with st.spinner('正在执行工作流...'):
                try:
                    result = self.workflow_manager.run_entire_workflow(params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        domain_result = result['results']['domain_fetcher']
                        framework_result = result['results']['framework_analyzer']
                        
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("域名获取成功", domain_result['success_count'])
                        with col2:
                            st.metric("域名获取失败", domain_result['total'] - domain_result['success_count'])
                        with col3:
                            st.metric("框架分析完成", framework_result['analyzed_count'])
                    else:
                        st.error(f"❌ {result['message']}")
                except Exception as e:
                    st.error(f"工作流执行失败: {str(e)}")
        
        # 重置工作流
        st.markdown("---")
        if st.button("🔄 重置整个工作流", type="secondary"):
            if st.session_state.get('confirm_reset', False):
                self.workflow_manager.reset_workflow()
                st.success("工作流已重置")
                st.session_state.confirm_reset = False
                st.rerun()
            else:
                st.session_state.confirm_reset = True
                st.warning("请再次点击确认重置")
    
    def _render_step_execution(self):
        """渲染步骤执行页面"""
        st.header("▶️ 步骤执行")
        
        # 选择步骤
        steps = self.workflow_manager.get_available_steps()
        step_options = {f"{info['name']}": step_id 
                       for step_id, info in steps.items()}
        
        selected_step_name = st.selectbox("选择要执行的步骤", list(step_options.keys()))
        selected_step_id = step_options[selected_step_name]
        
        # 根据步骤显示对应的参数输入界面
        if selected_step_id == 'domain_fetcher':
            self._render_domain_fetcher_params()
        elif selected_step_id == 'framework_analyzer':
            self._render_framework_analyzer_params()
        else:
            st.info("该步骤暂未实现参数配置界面")
    
    def _render_domain_fetcher_params(self):
        """渲染域名获取器参数界面"""
        st.subheader("🌐 域名源码获取配置")
        
        # 域名输入
        domains_input = st.text_area(
            "输入域名列表（每行一个）",
            height=150,
            placeholder="example.com\nwww.google.com\nhttps://github.com",
            value="https://httpbin.org/html\nhttps://www.github.com"  # 默认示例
        )
        
        # 获取方法选择
        method = st.selectbox(
            "选择获取方法",
            ["browser", "browser-use"],
            help="browser: 使用Selenium浏览器获取\nbrowser-use: 使用更智能的浏览器自动化（需要安装browser-use）"
        )
        
        # 执行按钮
        if st.button("🚀 开始获取", type="primary"):
            if not domains_input.strip():
                st.error("请输入至少一个域名")
                return
            
            # 解析域名列表
            domains = [line.strip() for line in domains_input.split('\n') if line.strip()]
            
            # 显示进度
            with st.spinner('正在获取域名源码...'):
                try:
                    # 执行步骤
                    params = {'domains': domains, 'method': method}
                    result = self.workflow_manager.run_step('domain_fetcher', params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("总计", result['total'])
                        with col2:
                            st.metric("成功", result['success_count'])
                        with col3:
                            st.metric("失败", result['total'] - result['success_count'])
                        
                        # 保存到数据库
                        self.workflow_manager.db_manager.save_domain_results(result['data'])
                    else:
                        st.error(f"❌ {result['message']}")
                
                except Exception as e:
                    st.error(f"执行失败: {str(e)}")
    
    def _render_framework_analyzer_params(self):
        """渲染框架分析器参数界面"""
        st.subheader("🔧 前端框架分析配置")
        
        # 检查是否有域名获取结果
        domain_results = self.workflow_manager.get_step_results('domain_fetcher')
        
        if not domain_results:
            st.warning("⚠️ 请先执行域名源码获取步骤")
            return
        
        # 显示可分析的域名数量
        successful_results = [r for r in domain_results if r.get('success', False)]
        st.info(f"找到 {len(successful_results)} 个成功获取源码的域名可供分析")
        
        # 执行按钮
        if st.button("🔍 开始分析", type="primary"):
            with st.spinner('正在分析前端框架...'):
                try:
                    # 执行分析
                    params = {'source_data': successful_results}
                    result = self.workflow_manager.run_step('framework_analyzer', params)
                    
                    if result['success']:
                        st.success(f"✅ {result['message']}")
                        
                        # 显示结果摘要
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("总计", len(successful_results))
                        with col2:
                            st.metric("已分析", result['analyzed_count'])
                        
                        # 保存到数据库
                        self.workflow_manager.db_manager.save_framework_analysis(result['data'])
                    else:
                        st.error(f"❌ {result['message']}")
                
                except Exception as e:
                    st.error(f"分析失败: {str(e)}")
    
    def _render_results_view(self):
        """渲染结果查看页面"""
        st.header("📊 结果查看")

        # 选择查看的结果类型
        default_result_type = "域名获取结果"
        if 'result_type' in st.session_state:
            default_result_type = st.session_state.result_type
            # 清除session state，避免影响后续操作
            del st.session_state.result_type

        result_types = ["域名获取结果", "框架分析结果"]
        default_index = result_types.index(default_result_type) if default_result_type in result_types else 0

        result_type = st.selectbox(
            "选择结果类型",
            result_types,
            index=default_index
        )
        
        if result_type == "域名获取结果":
            self._render_domain_results()
        elif result_type == "框架分析结果":
            self._render_framework_results()
    
    def _render_domain_results(self):
        """渲染域名获取结果"""
        results = self.workflow_manager.db_manager.get_domain_results(limit=100)
        
        if not results:
            st.info("暂无域名获取结果")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(results)
        
        # 过滤选项
        col1, col2 = st.columns(2)
        with col1:
            status_filter = st.selectbox("状态过滤", ["全部", "成功", "失败"])
        with col2:
            limit = st.number_input("显示数量", min_value=10, max_value=1000, value=50)
        
        # 应用过滤
        if status_filter != "全部":
            df = df[df['status'] == ('success' if status_filter == '成功' else 'failed')]
        
        df = df.head(limit)
        
        # 显示数据表
        if not df.empty:
            st.dataframe(
                df[['domain', 'status', 'status_code', 'response_time', 'content_length', 'created_at']],
                use_container_width=True
            )
            
            # 添加源码查看功能
            st.subheader("源码查看")
            selected_index = st.selectbox("选择域名查看源码", df.index, format_func=lambda i: df.loc[i, 'domain'])
            
            if selected_index is not None:
                source_code = df.loc[selected_index, 'source_code']
                if source_code:
                    st.code(source_code, language='html')
                else:
                    st.warning("该域名未成功获取源码")
            
            # 添加资源文件浏览器
            st.subheader("资源文件浏览器")
            if selected_index is not None and 'resources' in df.loc[selected_index]:
                resources = df.loc[selected_index, 'resources']
                
                # 创建树形结构
                tree = {
                    'scripts': resources.get('scripts', []),
                    'styles': resources.get('styles', []),
                    'images': resources.get('images', []),
                    'other': resources.get('other', [])
                }
                
                # 选择资源类型
                resource_type = st.selectbox("资源类型", ['scripts', 'styles', 'images', 'other'])
                
                if tree[resource_type]:
                    # 选择具体资源文件
                    file_options = [f"{file['filename']} ({file['type']})" for file in tree[resource_type]]
                    selected_file = st.selectbox("选择文件", file_options)
                    
                    if selected_file:
                        # 获取选中的文件URL
                        file_index = file_options.index(selected_file)
                        file_url = tree[resource_type][file_index]['url']
                        
                        # 显示文件内容
                        st.subheader(f"文件内容: {selected_file}")
                        st.write(f"**URL**: {file_url}")
                        
                        # 尝试获取文件内容
                        try:
                            response = requests.get(file_url, timeout=10)
                            if response.status_code == 200:
                                content_type = response.headers.get('Content-Type', '')
                                
                                # 根据内容类型显示
                                if 'text' in content_type:
                                    st.code(response.text, language='html' if 'html' in content_type else 
                                                                  'javascript' if 'javascript' in content_type else 
                                                                  'css' if 'css' in content_type else 
                                                                  'plaintext')
                                elif 'image' in content_type:
                                    st.image(response.content, caption=selected_file)
                                else:
                                    st.info("无法预览此文件类型")
                                    st.download_button(
                                        "下载文件",
                                        response.content,
                                        file_name=selected_file.split(' ')[0],
                                        mime=content_type
                                    )
                            else:
                                st.warning(f"获取文件内容失败: HTTP {response.status_code}")
                        except Exception as e:
                            st.error(f"获取文件内容失败: {str(e)}")
                else:
                    st.info(f"没有找到{resource_type}资源")
            else:
                st.info("没有资源文件信息")
            
            # 导出功能
            if st.button("📥 导出数据"):
                csv = df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    "下载 CSV 文件",
                    csv,
                    file_name=f"domain_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        else:
            st.info("没有符合条件的结果")
    
    def _render_framework_results(self):
        """渲染框架分析结果"""
        results = self.workflow_manager.db_manager.get_framework_analysis(limit=100)
        
        if not results:
            st.info("暂无框架分析结果")
            return
        
        # 显示结果列表
        for result in results[:20]:  # 限制显示数量
            with st.expander(f"🌐 {result['domain']}", expanded=False):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**检测到的框架:**")
                    if result['frameworks']:
                        for framework, info in result['frameworks'].items():
                            confidence = info.get('confidence', 0)
                            st.write(f"- {framework}: {confidence}% 置信度")
                    else:
                        st.write("未检测到主要框架")
                
                with col2:
                    st.write("**Source Map:**")
                    if result['has_sourcemap']:
                        st.write("✅ 发现 Source Map 文件")
                        for url in result['sourcemap_urls']:
                            st.write(f"- {url}")
                    else:
                        st.write("❌ 未发现 Source Map 文件")
    
    def _render_statistics(self):
        """渲染统计页面"""
        st.header("📈 数据统计")
        
        # 获取统计数据
        try:
            stats = self.workflow_manager.db_manager.get_statistics()
            
            # 基本统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总域名数", stats['total_domains'])
            with col2:
                st.metric("成功获取", stats['successful_domains'])
            with col3:
                st.metric("已分析", stats['analyzed_domains'])
            with col4:
                st.metric("含Source Map", stats['sourcemap_count'])
            
            # 成功率图表
            if stats['total_domains'] > 0:
                success_rate = stats['success_rate']
                fig = go.Figure(go.Indicator(
                    mode = "gauge+number",
                    value = success_rate,
                    title = {'text': "获取成功率 (%)"},
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 80], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 90
                        }
                    }
                ))
                st.plotly_chart(fig, use_container_width=True)
        except Exception as e:
            st.error(f"获取统计数据失败: {str(e)}")
    
    def _render_settings(self):
        """渲染设置页面"""
        st.header("⚙️ 系统设置")
        
        # 数据管理
        st.subheader("数据管理")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📁 导出所有数据"):
                try:
                    export_path = self.workflow_manager.db_manager.export_to_json()
                    st.success(f"数据已导出到: {export_path}")
                except Exception as e:
                    st.error(f"导出失败: {str(e)}")
        
        with col2:
            if st.button("🗑️ 清空所有数据", type="secondary"):
                if st.session_state.get('confirm_clear', False):
                    self.workflow_manager.db_manager.clear_all_data()
                    st.success("所有数据已清空")
                    st.session_state.confirm_clear = False
                    st.rerun()
                else:
                    st.session_state.confirm_clear = True
                    st.warning("请再次点击确认清空")
        
        # 系统信息
        st.subheader("系统信息")
        system_info = {
            "数据库路径": self.workflow_manager.db_manager.db_path,
            "可用步骤数": len(self.workflow_manager.steps),
            "工作流状态": self.workflow_manager.status.value
        }
        
        for key, value in system_info.items():
            st.write(f"**{key}**: {value}")
