#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP客户端测试脚本
用于测试Ane分析MCP服务器的功能
"""

import asyncio
import json
import subprocess
import sys
import os
from typing import Dict, Any, List

class MCPClient:
    """简单的MCP客户端用于测试"""
    
    def __init__(self, server_command: List[str]):
        self.server_command = server_command
        self.process = None
    
    async def start_server(self):
        """启动MCP服务器"""
        self.process = await asyncio.create_subprocess_exec(
            *self.server_command,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "ane-test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        await self._send_request(init_request)
        response = await self._read_response()
        print("初始化响应:", json.dumps(response, ensure_ascii=False, indent=2))
        
        return response
    
    async def list_tools(self):
        """列出可用工具"""
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        await self._send_request(request)
        response = await self._read_response()
        print("工具列表:", json.dumps(response, ensure_ascii=False, indent=2))
        
        return response
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用工具"""
        request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        }
        
        await self._send_request(request)
        response = await self._read_response()
        print(f"工具调用结果 ({tool_name}):", json.dumps(response, ensure_ascii=False, indent=2))
        
        return response
    
    async def _send_request(self, request: Dict[str, Any]):
        """发送请求到服务器"""
        if not self.process:
            raise RuntimeError("服务器未启动")
        
        request_str = json.dumps(request) + "\n"
        self.process.stdin.write(request_str.encode())
        await self.process.stdin.drain()
    
    async def _read_response(self) -> Dict[str, Any]:
        """从服务器读取响应"""
        if not self.process:
            raise RuntimeError("服务器未启动")
        
        line = await self.process.stdout.readline()
        if not line:
            raise RuntimeError("服务器连接断开")
        
        return json.loads(line.decode().strip())
    
    async def stop_server(self):
        """停止服务器"""
        if self.process:
            self.process.terminate()
            await self.process.wait()


async def test_website_analysis():
    """测试网站分析功能"""
    print("=== 测试Ane MCP服务器 ===\n")
    
    # 服务器命令
    server_script = os.path.join(os.path.dirname(__file__), "server.py")
    server_command = [sys.executable, server_script]
    
    client = MCPClient(server_command)
    
    try:
        # 启动服务器
        print("1. 启动MCP服务器...")
        await client.start_server()
        
        # 列出工具
        print("\n2. 列出可用工具...")
        await client.list_tools()
        
        # 测试网站分析
        print("\n3. 测试网站分析...")
        analysis_result = await client.call_tool("analyze_website", {
            "url": "http://localhost:8080/",
            "method": "requests",
            "include_content": True
        })
        
        # 测试信息提取
        print("\n4. 测试信息提取...")
        test_code = '''
        var apiKey = 'sk-1234567890abcdefghijklmnopqrstuvwxyz';
        var userPhone = '13812345678';
        var userEmail = '<EMAIL>';
        import { Component } from './component.js';
        '''
        
        extraction_result = await client.call_tool("extract_sensitive_info", {
            "content": test_code,
            "filename": "test.js"
        })
        
        # 测试代码分析
        print("\n5. 测试代码分析...")
        code_analysis_result = await client.call_tool("analyze_code_structure", {
            "content": test_code,
            "filename": "test.js"
        })
        
        # 获取状态
        print("\n6. 获取分析状态...")
        status_result = await client.call_tool("get_analysis_status", {})
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止服务器
        await client.stop_server()


def test_simple_analysis():
    """简单的分析测试（不使用MCP协议）"""
    print("=== 简单分析测试 ===\n")
    
    # 直接导入和使用服务器类
    sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
    
    try:
        from mcp_server.server import AneAnalysisServer
        
        server = AneAnalysisServer()
        
        # 测试网站分析
        print("测试网站分析...")
        
        # 这里需要异步运行，所以创建一个简单的测试
        async def run_test():
            result = await server._analyze_website({
                "url": "http://localhost:8080/",
                "method": "requests",
                "include_content": False
            })
            
            print("分析结果:")
            for content in result:
                print(content.text)
        
        asyncio.run(run_test())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "simple":
        test_simple_analysis()
    else:
        asyncio.run(test_website_analysis())
