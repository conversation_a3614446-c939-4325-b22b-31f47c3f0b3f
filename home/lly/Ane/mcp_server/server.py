#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ane MCP Server - 网站分析服务
提供URL分析、路径提取、信息提取等功能的MCP服务器
"""

import asyncio
import json
import sys
import os
from typing import Any, Dict, List, Optional
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

from src.workflow_manager import WorkflowManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AneAnalysisServer:
    """Ane网站分析MCP服务器"""
    
    def __init__(self):
        self.server = Server("ane-analysis-server")
        self.workflow_manager = WorkflowManager()
        self._setup_handlers()
    
    def _setup_handlers(self):
        """设置MCP服务器处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """列出可用的工具"""
            return [
                Tool(
                    name="analyze_website",
                    description="分析网站URL，提取所有资源文件和路径信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "要分析的网站URL"
                            },
                            "method": {
                                "type": "string",
                                "enum": ["requests", "browser"],
                                "default": "requests",
                                "description": "获取方法：requests或browser"
                            },
                            "include_content": {
                                "type": "boolean",
                                "default": True,
                                "description": "是否包含文件内容"
                            }
                        },
                        "required": ["url"]
                    }
                ),
                Tool(
                    name="extract_sensitive_info",
                    description="从代码内容中提取敏感信息",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": "要分析的代码内容"
                            },
                            "filename": {
                                "type": "string",
                                "default": "unknown",
                                "description": "文件名（可选）"
                            }
                        },
                        "required": ["content"]
                    }
                ),
                Tool(
                    name="analyze_code_structure",
                    description="分析代码结构和特征",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "content": {
                                "type": "string",
                                "description": "要分析的代码内容"
                            },
                            "filename": {
                                "type": "string",
                                "default": "unknown",
                                "description": "文件名（可选）"
                            }
                        },
                        "required": ["content"]
                    }
                ),
                Tool(
                    name="get_analysis_status",
                    description="获取分析状态和统计信息",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "additionalProperties": False
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            try:
                if name == "analyze_website":
                    return await self._analyze_website(arguments)
                elif name == "extract_sensitive_info":
                    return await self._extract_sensitive_info(arguments)
                elif name == "analyze_code_structure":
                    return await self._analyze_code_structure(arguments)
                elif name == "get_analysis_status":
                    return await self._get_analysis_status(arguments)
                else:
                    return [TextContent(
                        type="text",
                        text=f"未知工具: {name}"
                    )]
            except Exception as e:
                logger.error(f"工具调用错误 {name}: {str(e)}", exc_info=True)
                return [TextContent(
                    type="text",
                    text=f"工具执行失败: {str(e)}"
                )]
    
    async def _analyze_website(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """分析网站"""
        url = arguments.get("url")
        method = arguments.get("method", "requests")
        include_content = arguments.get("include_content", True)
        
        logger.info(f"开始分析网站: {url}")
        
        # 使用domain_fetcher获取网站信息
        params = {
            'domains': [url],
            'method': method
        }
        
        result = self.workflow_manager.run_step('domain_fetcher', params)
        
        if not result['success']:
            return [TextContent(
                type="text",
                text=f"网站分析失败: {result['message']}"
            )]
        
        # 处理分析结果
        analysis_data = result['data'][0] if result['data'] else {}
        
        # 构建路径分析结果
        path_analysis = self._build_path_analysis(analysis_data, include_content)
        
        return [TextContent(
            type="text",
            text=json.dumps(path_analysis, ensure_ascii=False, indent=2)
        )]
    
    async def _extract_sensitive_info(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """提取敏感信息"""
        content = arguments.get("content")
        filename = arguments.get("filename", "unknown")
        
        logger.info(f"开始提取敏感信息: {filename}")
        
        params = {
            'content': content,
            'filename': filename
        }
        
        result = self.workflow_manager.run_step('info_extractor', params)
        
        if not result['success']:
            return [TextContent(
                type="text",
                text=f"信息提取失败: {result['message']}"
            )]
        
        return [TextContent(
            type="text",
            text=json.dumps(result['data'], ensure_ascii=False, indent=2)
        )]
    
    async def _analyze_code_structure(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """分析代码结构"""
        content = arguments.get("content")
        filename = arguments.get("filename", "unknown")
        
        logger.info(f"开始分析代码结构: {filename}")
        
        params = {
            'file_content': content,
            'filename': filename
        }
        
        result = self.workflow_manager.run_step('code_analyzer', params)
        
        if not result['success']:
            return [TextContent(
                type="text",
                text=f"代码分析失败: {result['message']}"
            )]
        
        return [TextContent(
            type="text",
            text=json.dumps(result['data'], ensure_ascii=False, indent=2)
        )]
    
    async def _get_analysis_status(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """获取分析状态"""
        status = self.workflow_manager.get_workflow_status()
        
        return [TextContent(
            type="text",
            text=json.dumps(status, ensure_ascii=False, indent=2)
        )]
    
    def _build_path_analysis(self, analysis_data: Dict[str, Any], include_content: bool = True) -> Dict[str, Any]:
        """构建路径分析结果"""
        result = {
            "url": analysis_data.get('domain', ''),
            "success": analysis_data.get('success', False),
            "timestamp": analysis_data.get('timestamp', ''),
            "paths": {},
            "statistics": {
                "total_files": 0,
                "files_with_content": 0,
                "file_types": {}
            }
        }
        
        if not analysis_data.get('success'):
            result["error"] = analysis_data.get('error', '未知错误')
            return result
        
        # 处理主HTML文件
        if analysis_data.get('source_code'):
            result["paths"]["/"] = {
                "type": "html",
                "filename": "index.html",
                "size": len(analysis_data['source_code']),
                "has_content": True
            }
            
            if include_content:
                result["paths"]["/"]["content"] = analysis_data['source_code']
            
            result["statistics"]["total_files"] += 1
            result["statistics"]["files_with_content"] += 1
            result["statistics"]["file_types"]["html"] = result["statistics"]["file_types"].get("html", 0) + 1
        
        # 处理资源文件
        resources = analysis_data.get('resources', {})
        for resource_type, resource_list in resources.items():
            if not isinstance(resource_list, list):
                continue
            
            for resource in resource_list:
                url = resource.get('url', '')
                filename = resource.get('filename', 'unknown')
                
                # 从URL提取路径
                try:
                    from urllib.parse import urlparse
                    parsed = urlparse(url)
                    path = parsed.path if parsed.path else f"/{filename}"
                except:
                    path = f"/{filename}"
                
                file_info = {
                    "type": resource_type,
                    "filename": filename,
                    "url": url,
                    "size": resource.get('size', 0),
                    "has_content": bool(resource.get('content')),
                    "fetch_success": resource.get('fetch_success', False)
                }
                
                if include_content and resource.get('content'):
                    file_info["content"] = resource['content']
                
                if resource.get('fetch_error'):
                    file_info["fetch_error"] = resource['fetch_error']
                
                result["paths"][path] = file_info
                
                # 更新统计信息
                result["statistics"]["total_files"] += 1
                if resource.get('content'):
                    result["statistics"]["files_with_content"] += 1
                
                result["statistics"]["file_types"][resource_type] = result["statistics"]["file_types"].get(resource_type, 0) + 1
        
        return result
    
    async def run(self):
        """运行MCP服务器"""
        logger.info("启动Ane分析MCP服务器...")
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="ane-analysis-server",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities={}
                    )
                )
            )


async def main():
    """主函数"""
    server = AneAnalysisServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
