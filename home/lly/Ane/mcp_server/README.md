# Ane MCP Server

Ane网站分析MCP (Model Context Protocol) 服务器，提供完整的网站分析、路径提取和信息提取功能。

## 功能特性

### 🌐 网站分析 (`analyze_website`)
- **输入**: URL地址
- **输出**: 完整的网站路径分析结果
- **功能**:
  - 自动获取网站所有资源文件（HTML、CSS、JavaScript、图片等）
  - 提取文件路径和目录结构
  - 获取文件内容（可选）
  - 统计文件类型和数量
  - 支持requests和browser两种获取方式

### 🔍 敏感信息提取 (`extract_sensitive_info`)
- **输入**: 代码内容
- **输出**: 提取的敏感信息
- **功能**:
  - 身份证号码、手机号码、邮箱地址
  - API密钥、JWT Token、密码
  - IP地址、域名、URL
  - JavaScript导入路径
  - 加密算法调用

### 📊 代码结构分析 (`analyze_code_structure`)
- **输入**: 代码内容
- **输出**: 代码结构分析结果
- **功能**:
  - 代码复杂度分析
  - 函数和变量统计
  - 依赖关系分析
  - 代码质量评估

### 📈 分析状态 (`get_analysis_status`)
- **输出**: 当前分析状态和统计信息
- **功能**:
  - 工作流执行状态
  - 步骤完成情况
  - 性能统计

## 安装和使用

### 1. 安装依赖

```bash
# 安装MCP库
pip install mcp

# 安装其他依赖
pip install -r mcp_server/requirements.txt
```

### 2. 启动服务器

```bash
# 使用启动脚本
./mcp_server/start_server.sh

# 或直接运行
cd /home/<USER>/Ane
python mcp_server/server.py
```

### 3. 使用示例

#### 分析网站
```json
{
  "tool": "analyze_website",
  "arguments": {
    "url": "https://example.com",
    "method": "requests",
    "include_content": true
  }
}
```

**输出示例**:
```json
{
  "url": "https://example.com",
  "success": true,
  "timestamp": "2024-01-01T12:00:00Z",
  "paths": {
    "/": {
      "type": "html",
      "filename": "index.html",
      "size": 1024,
      "has_content": true,
      "content": "<!DOCTYPE html>..."
    },
    "/static/js/main.js": {
      "type": "scripts",
      "filename": "main.js",
      "url": "https://example.com/static/js/main.js",
      "size": 2048,
      "has_content": true,
      "fetch_success": true
    },
    "/static/css/style.css": {
      "type": "styles",
      "filename": "style.css",
      "url": "https://example.com/static/css/style.css",
      "size": 1536,
      "has_content": true,
      "fetch_success": true
    }
  },
  "statistics": {
    "total_files": 3,
    "files_with_content": 3,
    "file_types": {
      "html": 1,
      "scripts": 1,
      "styles": 1
    }
  }
}
```

#### 提取敏感信息
```json
{
  "tool": "extract_sensitive_info",
  "arguments": {
    "content": "var apiKey = 'sk-1234567890abcdefghijklmnopqrstuvwxyz'; var phone = '13812345678';",
    "filename": "config.js"
  }
}
```

**输出示例**:
```json
{
  "filename": "config.js",
  "source_length": 85,
  "extracted_info": {
    "secret": ["sk-1234567890abcdefghijklmnopqrstuvwxyz"],
    "mobile": ["13812345678"],
    "js_imports": [],
    "algorithm": []
  },
  "summary": {
    "可能的密钥": 1,
    "手机号码": 1
  }
}
```

## 测试

### 运行测试客户端
```bash
# 完整MCP协议测试
python mcp_server/client_test.py

# 简单功能测试
python mcp_server/client_test.py simple
```

### 测试本地服务器
确保测试服务器运行在 `http://localhost:8080/`：
```bash
python test_server.py
```

## 配置

服务器配置文件: `mcp_server/config.json`

```json
{
  "server": {
    "name": "ane-analysis-server",
    "version": "1.0.0"
  },
  "settings": {
    "max_concurrent_requests": 10,
    "request_timeout": 60,
    "enable_browser_mode": true,
    "max_file_size": 10485760
  }
}
```

## API参考

### analyze_website

**参数**:
- `url` (string, required): 要分析的网站URL
- `method` (string, optional): 获取方法，"requests" 或 "browser"，默认 "requests"
- `include_content` (boolean, optional): 是否包含文件内容，默认 true

**返回**: 网站路径分析结果的JSON对象

### extract_sensitive_info

**参数**:
- `content` (string, required): 要分析的代码内容
- `filename` (string, optional): 文件名，默认 "unknown"

**返回**: 敏感信息提取结果的JSON对象

### analyze_code_structure

**参数**:
- `content` (string, required): 要分析的代码内容
- `filename` (string, optional): 文件名，默认 "unknown"

**返回**: 代码结构分析结果的JSON对象

### get_analysis_status

**参数**: 无

**返回**: 分析状态和统计信息的JSON对象

## 错误处理

服务器会返回详细的错误信息，包括：
- 网络连接错误
- URL格式错误
- 内容解析错误
- 超时错误

## 日志

服务器日志保存在 `ane_mcp_server.log` 文件中，包含详细的执行信息和错误记录。

## 许可证

MIT License
