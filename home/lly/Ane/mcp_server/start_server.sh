#!/bin/bash
# Ane MCP服务器启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3"
    exit 1
fi

# 检查依赖
echo "检查依赖..."
python3 -c "import mcp" 2>/dev/null || {
    echo "错误: 未安装mcp库，请运行: pip install mcp"
    exit 1
}

# 启动服务器
echo "启动Ane MCP服务器..."
echo "项目根目录: $PROJECT_ROOT"
echo "服务器脚本: $SCRIPT_DIR/server.py"

cd "$PROJECT_ROOT"
python3 "$SCRIPT_DIR/server.py"
