#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信息提取测试页面 - 独立测试信息提取功能
"""

import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.workflow_manager import WorkflowManager

def main():
    st.set_page_config(
        page_title="信息提取测试",
        page_icon="📋",
        layout="wide"
    )
    
    st.title("📋 信息提取测试工具")
    st.markdown("---")
    
    # 创建工作流管理器
    if 'workflow_manager' not in st.session_state:
        st.session_state.workflow_manager = WorkflowManager()
    
    workflow_manager = st.session_state.workflow_manager
    
    # 输入区域
    st.subheader("📝 输入内容")
    
    # 选择输入方式
    input_method = st.radio(
        "选择输入方式：",
        ["直接输入", "上传文件", "使用示例"],
        horizontal=True
    )
    
    content = ""
    filename = "test_input"
    
    if input_method == "直接输入":
        content = st.text_area(
            "请输入HTML或JavaScript内容：",
            height=300,
            placeholder="在这里粘贴您的HTML或JavaScript代码..."
        )
        filename = st.text_input("文件名（可选）：", value="manual_input.html")
        
    elif input_method == "上传文件":
        uploaded_file = st.file_uploader(
            "选择文件",
            type=['html', 'htm', 'js', 'jsx', 'ts', 'tsx', 'css', 'txt'],
            help="支持HTML、JavaScript、CSS等文件格式"
        )
        
        if uploaded_file is not None:
            try:
                content = uploaded_file.read().decode('utf-8')
                filename = uploaded_file.name
                st.success(f"✅ 文件已加载：{filename} ({len(content)} 字符)")
            except Exception as e:
                st.error(f"文件读取失败：{str(e)}")
                
    elif input_method == "使用示例":
        example_choice = st.selectbox(
            "选择示例：",
            ["包含敏感信息的HTML", "JavaScript API调用", "包含加密算法的代码"]
        )
        
        examples = {
            "包含敏感信息的HTML": '''<!DOCTYPE html>
<html>
<head>
    <title>用户信息页面</title>
    <script>
        var apiKey = 'sk-1234567890abcdefghijklmnopqrstuvwxyz';
        var userPhone = '13812345678';
        var userEmail = '<EMAIL>';
        var userId = '110101199001011234';
        var serverIp = '************0:8080';
        var apiUrl = 'https://api.example.com/v1/users';
        var jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
    </script>
</head>
<body>
    <h1>用户信息</h1>
    <p>联系电话：13987654321</p>
    <p>邮箱：<EMAIL></p>
    <p>服务器：********</p>
</body>
</html>''',
            
            "JavaScript API调用": '''// API配置
const config = {
    apiKey: 'pk_live_51H7qABC123456789',
    secretKey: 'sk_test_4eC39HqLyjWDarjtT1zdp7dc',
    baseUrl: 'https://api.stripe.com/v1',
    webhookSecret: 'whsec_1234567890abcdef'
};

// 用户数据
const userData = {
    phone: '15912345678',
    email: '<EMAIL>',
    idCard: '320102199001011234',
    address: '江苏省南京市玄武区'
};

// 服务器配置
const servers = [
    'https://server1.example.com:443',
    '************:3000',
    'db.internal.com'
];

// JWT处理
function generateToken(payload) {
    const secret = 'my-super-secret-key-2023';
    return jwt.sign(payload, secret);
}''',
            
            "包含加密算法的代码": '''// 加密工具类
class CryptoUtils {
    constructor() {
        this.aesKey = 'AES256-SECRET-KEY-2023';
        this.rsaPrivateKey = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB...
-----END PRIVATE KEY-----`;
    }
    
    // AES加密
    encryptAES(data) {
        return CryptoJS.AES.encrypt(data, this.aesKey).toString();
    }
    
    // MD5哈希
    hashMD5(data) {
        return md5(data);
    }
    
    // SHA256哈希
    hashSHA256(data) {
        return sha256(data);
    }
    
    // Base64编码
    encodeBase64(data) {
        return btoa(data);
    }
}

// 数据库连接
const dbConfig = {
    host: '************0',
    port: 3306,
    username: 'admin',
    password: 'P@ssw0rd123!',
    database: 'user_data'
};'''
        }
        
        content = examples[example_choice]
        filename = f"{example_choice.replace(' ', '_').lower()}.html"
    
    # 提取按钮和结果显示
    if content.strip():
        st.markdown("---")
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔍 开始信息提取", type="primary", use_container_width=True):
                with st.spinner('正在提取敏感信息...'):
                    try:
                        params = {
                            'content': content,
                            'filename': filename
                        }
                        
                        result = workflow_manager.run_step('info_extractor', params)
                        
                        if result['success']:
                            st.success(f"✅ {result['message']}")
                            
                            # 显示提取结果
                            st.markdown("---")
                            display_extraction_result(result['data'])
                        else:
                            st.error(f"❌ {result['message']}")
                            
                    except Exception as e:
                        st.error(f"信息提取失败: {str(e)}")
    else:
        st.info("👆 请先输入或选择要分析的内容")

def display_extraction_result(extract_data):
    """显示信息提取结果"""
    st.subheader("📋 信息提取结果")
    
    extracted_info = extract_data.get('extracted_info', {})
    summary = extract_data.get('summary', {})
    
    # 显示摘要
    if summary:
        st.subheader("📊 提取摘要")
        
        # 创建摘要卡片
        cols = st.columns(min(4, len(summary)))
        for i, (info_type, count) in enumerate(summary.items()):
            with cols[i % 4]:
                # 根据类型选择颜色
                if '密钥' in info_type or 'Token' in info_type:
                    delta_color = "inverse"
                elif '身份证' in info_type or '手机' in info_type:
                    delta_color = "off"
                else:
                    delta_color = "normal"
                    
                st.metric(
                    label=info_type,
                    value=count,
                    delta=f"{count} 项",
                    delta_color=delta_color
                )
    
    # 显示详细信息
    if extracted_info:
        st.subheader("🔍 详细信息")
        
        # 按重要性排序显示
        priority_order = ['secret', 'jwt', 'sfz', 'mobile', 'mail', 'ip_port', 'ip', 'domain', 'url', 'algorithm', 'path', 'incomplete_path']
        
        for info_type in priority_order:
            items = extracted_info.get(info_type, [])
            if not items:
                continue
            
            # 中文显示名称和图标
            type_info = {
                'sfz': ('🆔 身份证号', True),
                'mobile': ('📱 手机号码', True),
                'mail': ('📧 邮箱地址', False),
                'ip': ('🌐 IP地址', False),
                'ip_port': ('🔌 IP:端口', False),
                'domain': ('🌍 域名', False),
                'path': ('📁 路径', False),
                'incomplete_path': ('📂 不完整路径', False),
                'url': ('🔗 URL', False),
                'jwt': ('🔑 JWT Token', True),
                'algorithm': ('🔐 加密算法', False),
                'secret': ('🔒 可能的密钥', True)
            }
            
            display_name, is_sensitive = type_info.get(info_type, (info_type, False))
            
            # 根据敏感程度决定是否默认展开
            expanded = info_type in ['secret', 'jwt', 'sfz', 'mobile']
            
            with st.expander(f"{display_name} ({len(items)} 个)", expanded=expanded):
                for i, item in enumerate(items[:50]):  # 限制显示数量
                    col1, col2 = st.columns([3, 1])
                    
                    with col1:
                        # 对敏感信息进行部分遮蔽
                        if is_sensitive and len(item) > 8:
                            masked_item = item[:4] + '*' * (len(item) - 8) + item[-4:]
                            st.write(f"**{i+1}.** `{masked_item}`")
                        else:
                            st.write(f"**{i+1}.** `{item}`")
                    
                    with col2:
                        if is_sensitive:
                            # 显示完整内容的按钮
                            if st.button("👁️", key=f"show_{info_type}_{i}", help="显示完整内容"):
                                st.write(f"完整内容: `{item}`")
                
                if len(items) > 50:
                    st.info(f"还有 {len(items) - 50} 个项目未显示")
        
        # 显示其他未分类的信息
        other_types = set(extracted_info.keys()) - set(priority_order)
        for info_type in other_types:
            items = extracted_info.get(info_type, [])
            if items:
                with st.expander(f"📄 {info_type} ({len(items)} 个)"):
                    for i, item in enumerate(items[:20]):
                        st.write(f"{i+1}. `{item}`")
    else:
        st.info("未发现敏感信息")
    
    # 导出功能
    st.markdown("---")
    st.subheader("📤 导出结果")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("📋 复制为JSON", use_container_width=True):
            import json
            json_result = json.dumps(extract_data, ensure_ascii=False, indent=2)
            st.code(json_result, language='json')
    
    with col2:
        if st.button("📄 生成报告", use_container_width=True):
            generate_report(extract_data)

def generate_report(extract_data):
    """生成提取报告"""
    st.subheader("📄 信息提取报告")
    
    report = f"""
# 信息提取报告

**文件名**: {extract_data.get('filename', 'unknown')}
**源码长度**: {extract_data.get('source_length', 0)} 字符
**提取时间**: {extract_data.get('timestamp', 'unknown')}

## 摘要
"""
    
    summary = extract_data.get('summary', {})
    for info_type, count in summary.items():
        report += f"- {info_type}: {count} 个\n"
    
    report += "\n## 详细信息\n"
    
    extracted_info = extract_data.get('extracted_info', {})
    for info_type, items in extracted_info.items():
        if items:
            report += f"\n### {info_type}\n"
            for i, item in enumerate(items[:10], 1):
                report += f"{i}. {item}\n"
            if len(items) > 10:
                report += f"... 还有 {len(items) - 10} 个项目\n"
    
    st.text_area("报告内容", report, height=400)
    
    # 下载按钮
    st.download_button(
        label="💾 下载报告",
        data=report,
        file_name=f"info_extract_report_{extract_data.get('filename', 'unknown')}.md",
        mime="text/markdown"
    )

if __name__ == "__main__":
    main()
