# 🚀 部署和运维指南

## 生产环境部署

### 1. 环境准备
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装额外的生产依赖
pip install gunicorn supervisor
```

### 2. 配置文件
创建生产配置 `config/production.py`:
```python
import os

# 数据库配置
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///data/analyzer.db')

# 安全配置
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key')

# 网络配置
REQUEST_TIMEOUT = 60
MAX_CONCURRENT = 10

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FILE = '/var/log/ane/app.log'
```

### 3. Systemd 服务
创建 `/etc/systemd/system/ane.service`:
```ini
[Unit]
Description=Ane Web Analyzer
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/ane
Environment=PATH=/opt/ane/venv/bin
ExecStart=/opt/ane/venv/bin/streamlit run main.py --server.port 8501
Restart=always

[Install]
WantedBy=multi-user.target
```

### 4. Nginx 反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Docker 部署

### 1. Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "main.py", "--server.port", "8501", "--server.address", "0.0.0.0"]
```

### 2. docker-compose.yml
```yaml
version: '3.8'
services:
  ane:
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/database
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
```

### 3. 构建和运行
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 监控和日志

### 1. 应用监控
可以集成 Prometheus + Grafana:
```python
from prometheus_client import Counter, Histogram
import time

REQUEST_COUNT = Counter('ane_requests_total', 'Total requests')
REQUEST_LATENCY = Histogram('ane_request_duration_seconds', 'Request latency')

def monitor_step_execution(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            REQUEST_COUNT.inc()
            return result
        finally:
            REQUEST_LATENCY.observe(time.time() - start_time)
    return wrapper
```

### 2. 日志配置
```python
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            RotatingFileHandler('logs/app.log', maxBytes=10485760, backupCount=5),
            logging.StreamHandler()
        ]
    )
```

### 3. 健康检查
```python
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    }
```

## 数据备份

### 1. 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/ane"
DB_FILE="database/analyzer.db"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_FILE $BACKUP_DIR/analyzer_$DATE.db

# 压缩备份
gzip $BACKUP_DIR/analyzer_$DATE.db

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

### 2. 定时任务
```bash
# 添加到 crontab
0 2 * * * /opt/ane/backup.sh
```

## 性能调优

### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_domains_domain ON domains(domain);
CREATE INDEX idx_domains_created_at ON domains(created_at);
CREATE INDEX idx_framework_analysis_domain ON framework_analysis(domain);
```

### 2. 缓存策略
- Redis 缓存热点数据
- 文件系统缓存源码
- 内存缓存分析结果

### 3. 负载均衡
- 多实例部署
- 数据库读写分离
- CDN 加速静态资源

