#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试服务器 - 提供测试页面
"""

from http.server import HTTPServer, SimpleHTTPRequestHandler
import os

class TestHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            # 创建一个包含多种前端技术的测试页面
            html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 前端技术栈</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端技术栈测试页面</h1>
        
        <div class="section">
            <h2>React 组件示例</h2>
            <div id="react-app"></div>
        </div>
        
        <div class="section">
            <h2>Vue 组件示例</h2>
            <div id="vue-app">
                <p v-if="showMessage">{{ message }}</p>
                <button @click="toggleMessage" class="btn">切换消息</button>
            </div>
        </div>
        
        <div class="section">
            <h2>jQuery 示例</h2>
            <button id="jquery-btn" class="btn">jQuery 按钮</button>
            <p id="jquery-result"></p>
        </div>
        
        <div class="section">
            <h2>原生 JavaScript</h2>
            <button onclick="showAlert()" class="btn">显示警告</button>
        </div>
    </div>

    <!-- React -->
    <script src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
    
    <!-- Vue -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <script>
        // React 组件
        const { useState } = React;
        
        function ReactComponent() {
            const [count, setCount] = useState(0);
            
            return React.createElement('div', null,
                React.createElement('p', null, '计数器: ' + count),
                React.createElement('button', {
                    className: 'btn',
                    onClick: () => setCount(count + 1)
                }, '增加')
            );
        }
        
        ReactDOM.render(React.createElement(ReactComponent), document.getElementById('react-app'));
        
        // Vue 应用
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    message: '这是一个Vue消息',
                    showMessage: true
                }
            },
            methods: {
                toggleMessage() {
                    this.showMessage = !this.showMessage;
                }
            }
        }).mount('#vue-app');
        
        // jQuery 代码
        $(document).ready(function() {
            $('#jquery-btn').click(function() {
                $('#jquery-result').text('jQuery 正在工作！');
            });
        });
        
        // 原生 JavaScript
        function showAlert() {
            alert('这是原生 JavaScript 警告！');
        }
        
        // 模拟 webpack 打包后的代码
        (function(modules) {
            var installedModules = {};
            function __webpack_require__(moduleId) {
                if(installedModules[moduleId]) {
                    return installedModules[moduleId].exports;
                }
                var module = installedModules[moduleId] = {
                    i: moduleId,
                    l: false,
                    exports: {}
                };
                modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
                module.l = true;
                return module.exports;
            }
            return __webpack_require__(0);
        })([
            function(module, exports, __webpack_require__) {
                console.log('这是一个模拟的webpack模块');
            }
        ]);
        
        // 添加一些可能的安全问题（用于测试漏洞审计）
        function unsafeFunction(userInput) {
            // XSS 漏洞示例
            document.getElementById('result').innerHTML = userInput;
            
            // 硬编码的API密钥（安全问题）
            const apiKey = 'sk-1234567890abcdef';
            
            // 不安全的eval使用
            eval('console.log("' + userInput + '")');
        }
        
        // Source Map 注释
        //# sourceMappingURL=app.js.map
    </script>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode('utf-8'))
        else:
            super().do_GET()

if __name__ == '__main__':
    port = 8080
    server = HTTPServer(('localhost', port), TestHandler)
    print(f"测试服务器启动在 http://localhost:{port}/")
    print("按 Ctrl+C 停止服务器")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        server.shutdown()
