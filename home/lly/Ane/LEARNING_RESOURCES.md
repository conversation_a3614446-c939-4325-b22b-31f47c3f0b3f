# 📚 学习资源和最佳实践

## 相关技术学习资源

### Python Web 开发
- **Streamlit 官方文档**: https://docs.streamlit.io/
- **FastAPI 教程**: https://fastapi.tiangolo.com/
- **异步编程指南**: https://docs.python.org/3/library/asyncio.html

### 网页分析技术
- **Web Scraping 最佳实践**: 
  - 尊重 robots.txt
  - 设置合理的请求间隔
  - 使用适当的 User-Agent
  - 处理反爬虫机制

- **前端技术检测方法**:
  - JavaScript 库指纹识别
  - CSS 框架特征检测
  - 构建工具痕迹分析

### 数据库设计
- **SQLite 优化**:
  - 索引策略
  - 查询优化
  - 事务管理
  - 备份恢复

## 开发最佳实践

### 1. 代码结构
```
# 好的实践
src/
├── models/          # 数据模型
├── services/        # 业务逻辑
├── utils/           # 工具函数
├── steps/           # 分析步骤
└── exceptions/      # 自定义异常
```

### 2. 错误处理
```python
# 统一异常处理
class AnalyzerException(Exception):
    """分析器基础异常"""
    pass

class NetworkException(AnalyzerException):
    """网络相关异常"""
    pass

class ValidationException(AnalyzerException):
    """数据验证异常"""
    pass
```

### 3. 配置管理
```python
# 使用 Pydantic 进行配置验证
from pydantic import BaseSettings

class Settings(BaseSettings):
    database_url: str = "sqlite:///analyzer.db"
    request_timeout: int = 30
    max_concurrent: int = 5
    
    class Config:
        env_file = ".env"
```

### 4. 日志记录
```python
import logging
import structlog

# 结构化日志
logger = structlog.get_logger()

def analyze_domain(domain: str):
    logger.info("开始分析域名", domain=domain)
    try:
        # 分析逻辑
        logger.info("分析完成", domain=domain, status="success")
    except Exception as e:
        logger.error("分析失败", domain=domain, error=str(e))
```

## 性能优化技巧

### 1. 并发控制
```python
import asyncio
from asyncio import Semaphore

async def fetch_with_limit(urls, limit=10):
    semaphore = Semaphore(limit)
    
    async def fetch_one(url):
        async with semaphore:
            return await fetch_url(url)
    
    tasks = [fetch_one(url) for url in urls]
    return await asyncio.gather(*tasks)
```

### 2. 缓存策略
```python
from functools import lru_cache
import redis

# 内存缓存
@lru_cache(maxsize=128)
def get_domain_info(domain):
    return expensive_operation(domain)

# Redis 缓存
def get_cached_result(key):
    cache = redis.Redis()
    result = cache.get(key)
    if result:
        return json.loads(result)
    return None
```

### 3. 数据库优化
```sql
-- 创建复合索引
CREATE INDEX idx_domain_created ON domains(domain, created_at);

-- 分页查询优化
SELECT * FROM domains 
WHERE id > :last_id 
ORDER BY id 
LIMIT :page_size;
```

## 安全考虑

### 1. 输入验证
```python
import validators
from urllib.parse import urlparse

def validate_domain(domain: str) -> bool:
    # URL 格式验证
    if not validators.url(domain):
        return False
    
    # 域名白名单检查
    parsed = urlparse(domain)
    if parsed.hostname in BLOCKED_DOMAINS:
        return False
    
    return True
```

### 2. 请求限制
```python
from time import sleep
import random

def respectful_request(url):
    # 随机延迟，避免被识别为机器人
    sleep(random.uniform(1, 3))
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (compatible; WebAnalyzer/1.0)',
        'Accept': 'text/html,application/xhtml+xml',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    return requests.get(url, headers=headers, timeout=30)
```

### 3. 数据脱敏
```python
import re

def sanitize_content(content: str) -> str:
    # 移除敏感信息
    content = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '[EMAIL]', content)
    content = re.sub(r'\b\d{3}-\d{2}-\d{4}\b', '[SSN]', content)
    content = re.sub(r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', '[CARD]', content)
    return content
```

## 测试策略

### 1. 单元测试
```python
import pytest
from unittest.mock import Mock, patch

class TestDomainFetcher:
    def test_validate_params_success(self):
        fetcher = DomainFetcher('test', 'test', 'test')
        assert fetcher.validate_params({'domains': ['test.com']})
    
    def test_validate_params_failure(self):
        fetcher = DomainFetcher('test', 'test', 'test')
        assert not fetcher.validate_params({})
    
    @patch('requests.get')
    def test_fetch_success(self, mock_get):
        mock_response = Mock()
        mock_response.text = '<html>test</html>'
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        fetcher = DomainFetcher('test', 'test', 'test')
        result = fetcher.execute({'domains': ['test.com']})
        
        assert result['success']
        assert len(result['data']) == 1
```

### 2. 集成测试
```python
def test_full_workflow():
    wm = WorkflowManager()
    
    # 测试步骤1
    result1 = wm.run_step('domain_fetcher', {
        'domains': ['https://httpbin.org/html']
    })
    assert result1['success']
    
    # 测试步骤2
    result2 = wm.run_step('framework_analyzer', {
        'source_data': result1['data']
    })
    assert result2['success']
    
    # 验证数据持久化
    saved_results = wm.db_manager.get_domain_results()
    assert len(saved_results) > 0
```

### 3. 性能测试
```python
import time
import concurrent.futures

def test_concurrent_processing():
    domains = [f'https://httpbin.org/delay/{i}' for i in range(10)]
    
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(process_domain, domain) for domain in domains]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    
    # 验证并发处理效果
    assert end_time - start_time < 60  # 应该在1分钟内完成
    assert len(results) == 10
```

这些资源和实践可以帮助您更好地理解和扩展 Ane 项目！

