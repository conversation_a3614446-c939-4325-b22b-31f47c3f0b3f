#!/bin/bash
# Ane Web Analyzer 启动脚本 - 使用指定虚拟环境

VENV_PATH="~/pro/pyvenv"
EXPANDED_VENV_PATH=$(eval echo $VENV_PATH)

echo "🔍 正在启动 Ane Web Analyzer..."

# 检查虚拟环境是否存在
if [ ! -d "$EXPANDED_VENV_PATH" ]; then
    echo "❌ 虚拟环境不存在: $VENV_PATH"
    echo "请先创建虚拟环境："
    echo "python3 -m venv ~/pro/pyvenv"
    exit 1
fi

echo "✅ 找到虚拟环境: $VENV_PATH"

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source $EXPANDED_VENV_PATH/bin/activate

if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"
else
    echo "❌ 虚拟环境激活失败"
    exit 1
fi

# 检查Python版本
echo "✅ Python版本: $(python --version)"

# 检查并安装依赖
echo "🔍 检查依赖包..."

# 核心依赖包列表
REQUIRED_PACKAGES=(
    "streamlit"
    "requests" 
    "beautifulsoup4"
    "pandas"
    "plotly"
)

# 检查每个包
MISSING_PACKAGES=()
for package in "${REQUIRED_PACKAGES[@]}"; do
    if python -c "import ${package//-/_}" 2>/dev/null; then
        echo "✅ $package 已安装"
    else
        echo "❌ $package 未安装"
        MISSING_PACKAGES+=("$package")
    fi
done

# 安装缺失的包
if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    echo ""
    echo "📦 正在安装缺失的依赖包..."
    for package in "${MISSING_PACKAGES[@]}"; do
        echo "正在安装 $package..."
        pip install "$package"
    done
    echo "✅ 依赖安装完成"
else
    echo "✅ 所有依赖已满足"
fi

# 创建必要目录
mkdir -p database logs

echo ""
echo "🚀 启动Web界面..."
echo "📱 访问地址: http://localhost:8501"
echo "⏹️  停止服务: Ctrl+C"
echo "🔧 当前环境: $VIRTUAL_ENV"
echo ""

# 启动应用
streamlit run main.py --server.port 8501 --server.address 0.0.0.0

